from flask import Flask
from flask_cors import CORS
from app.routes import auth_routes, job_routes, cv_routes, ai_routes, dashboard_routes, user_routes
import os

# Determine the base directory for the project
BASE_DIR = os.path.abspath(os.path.dirname(__file__))

def create_app():
    app = Flask(__name__, static_folder=os.path.join(BASE_DIR, '..', '..', 'Frontend', 'dist'))
    
    # Check for LaTeX dependencies
    from app.services.latex import LaTeXService
    latex_service = LaTeXService()
    if not latex_service.check_latex_installation():
        app.logger.warning("WARNING: LaTeX not installed. PDF generation will not work properly.\n"
                         "Please install TeXLive or MikTeX package to enable PDF generation.")

    # Enable CORS for all routes and origins (during development)
    CORS(app, resources={r"/api/*": {"origins": "*"}})
    
    # Add CORS headers to all responses
    @app.after_request
    def add_cors_headers(response):
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS')
        return response

    # Register your blueprints here
    app.register_blueprint(auth_routes.bp)
    app.register_blueprint(job_routes.bp)
    app.register_blueprint(cv_routes.bp)
    app.register_blueprint(ai_routes.ai_bp)
    app.register_blueprint(dashboard_routes.dashboard_bp, url_prefix='/api/dashboard')
    app.register_blueprint(user_routes.bp, url_prefix='/api/user')

    @app.route('/')
    def index():
        return {"status": "ok", "message": "JOB-AI Backend API is running"}

    @app.route('/health')
    def health():
        return {"status": "healthy", "message": "Backend is running properly"}

    return app
