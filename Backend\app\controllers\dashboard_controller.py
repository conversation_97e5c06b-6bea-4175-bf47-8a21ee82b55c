from flask import request, jsonify
from app.services.dashboard_service import get_dashboard_stats, get_upcoming_reminders
from firebase_admin import auth
import logging

def get_dashboard_data_controller():
    try:
        # Get the user's token from the Authorization header
        id_token = request.headers.get('Authorization').split('Bearer ')[1]
        if not id_token:
            return jsonify({"error": "Authorization token is missing"}), 401

        # Verify the token and get the user's UID
        decoded_token = auth.verify_id_token(id_token)
        user_id = decoded_token['uid']

        # Get the period from query parameters (e.g., '7', '30', '90', 'all')
        period = request.args.get('period', 'all') # Default to 'all' if not provided

        # Get stats from the service
        stats = get_dashboard_stats(user_id, period)
        reminders = get_upcoming_reminders(user_id)

        dashboard_data = {
            "stats": stats,
            "reminders": reminders
        }

        return jsonify(dashboard_data), 200
    except auth.InvalidIdTokenError:
        return jsonify({"error": "Invalid authorization token"}), 401
    except Exception as e:
        logging.error(f"Error in get_dashboard_data_controller: {e}")
        return jsonify({"error": "An internal error occurred", "details": str(e)}), 500
