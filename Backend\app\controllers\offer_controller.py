from flask import request, jsonify
from app.services.rag_job_parser import parse_offer

def parse_offer_controller():
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({"error": "Le champ 'text' est requis"}), 400
        
        raw_text = data.get("text")
        job_url = data.get("url")
        
        parsed = parse_offer(raw_text)
        
        if job_url and isinstance(parsed, dict) and 'url' in parsed:
            parsed['url'] = job_url
            
        return jsonify(parsed), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500
