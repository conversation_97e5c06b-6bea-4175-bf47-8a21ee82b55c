from flask import request, jsonify
from firebase_admin import auth
from app.services.user_service import get_user_profile, update_user_profile

def get_profile_controller():
    try:
        id_token = request.headers.get('Authorization').split('Bearer ')[1]
        decoded_token = auth.verify_id_token(id_token)
        user_id = decoded_token['uid']
        
        profile = get_user_profile(user_id)
        # Add user's email from token, as it's the source of truth
        profile['email'] = decoded_token.get('email', '')
        
        return jsonify(profile), 200
    except auth.InvalidIdTokenError:
        return jsonify({"error": "Invalid authorization token"}), 401
    except Exception as e:
        return jsonify({"error": str(e)}), 500

def update_profile_controller():
    try:
        id_token = request.headers.get('Authorization').split('Bearer ')[1]
        decoded_token = auth.verify_id_token(id_token)
        user_id = decoded_token['uid']
        
        profile_data = request.get_json()
        if not profile_data:
            return jsonify({"error": "No data provided"}), 400

        result = update_user_profile(user_id, profile_data)
        return jsonify(result), 200
    except auth.InvalidIdTokenError:
        return jsonify({"error": "Invalid authorization token"}), 401
    except Exception as e:
        return jsonify({"error": str(e)}), 500

def change_password_controller():
    try:
        id_token = request.headers.get('Authorization').split('Bearer ')[1]
        decoded_token = auth.verify_id_token(id_token)
        user_id = decoded_token['uid']
        
        data = request.get_json()
        new_password = data.get('password')

        if not new_password or len(new_password) < 6:
            return jsonify({"error": "Password must be at least 6 characters long"}), 400

        auth.update_user(user_id, password=new_password)
        
        return jsonify({"message": "Password updated successfully"}), 200
    except auth.InvalidIdTokenError:
        return jsonify({"error": "Invalid authorization token"}), 401
    except Exception as e:
        return jsonify({"error": str(e)}), 500
