from flask import Blueprint, request, jsonify
import os # Keep for now, though API key is not used for Arbeitnow
import requests
# import time # No longer simulating delay with a real API

ai_bp = Blueprint('ai_bp', __name__, url_prefix='/ai')

@ai_bp.route('/search-jobs', methods=['POST'])
def search_jobs_route():
    preferences = request.get_json()
    if not preferences:
        return jsonify({"error": "Invalid preferences"}), 400

    print(f"Received preferences: {preferences}")

    # --- Integration with Arbeitnow API ---
    api_url = "https://www.arbeitnow.com/api/job-board-api"

    # Arbeitnow does not require an API key based on documentation provided
    # headers = {} # No specific headers like API keys needed for Arbeitnow public endpoint

    # --- Map frontend preferences to Arbeitnow API parameters ---
    # IMPORTANT: Verify these parameter names from Arbeitnow's full API documentation!
    # Common names are used here as placeholders (e.g., 'search', 'location').
    query_params = {}
    
    # For job titles/keywords:
    # Check Arbeitnow docs for the correct parameter (e.g., 'q', 'search', 'keywords', 'description')
    if preferences.get('jobTitles'):
        job_title_keywords = " ".join(preferences['jobTitles'])
        query_params['search'] = job_title_keywords # ASSUMPTION: 'search' parameter
    
    # For locations:
    # Check Arbeitnow docs for the correct parameter (e.g., 'location', 'loc')
    if preferences.get('locations') and not preferences.get('remoteOnly'): # Only add location if not exclusively remote
        location_keywords = " ".join(preferences['locations'])
        query_params['location'] = location_keywords # ASSUMPTION: 'location' parameter

    if preferences.get('remoteOnly'):
        query_params['remote'] = 'true' # This parameter was mentioned in their blog
    
    # Arbeitnow API paginates, by default might return 100 results. 
    # Add 'page' parameter if you want to implement pagination based on more user preferences.
    # query_params['page'] = preferences.get('page', 1) 

    print(f"Querying Arbeitnow API with params: {query_params}")

    try:
        response = requests.get(api_url, params=query_params, timeout=15) # No headers for API key
        response.raise_for_status()  # Raises an HTTPError for bad responses (4XX or 5XX)
        
        api_response_data = response.json()
        # print(f"Raw API Response: {api_response_data}") # For debugging

        # --- Transform API response to frontend format ---
        jobs = []
        if api_response_data and 'data' in api_response_data: # Arbeitnow uses 'data' key for the list
            for job_item in api_response_data['data']:
                # The 'description' from Arbeitnow is HTML, frontend needs to handle it or strip tags.
                transformed_job = {
                    "id": job_item.get('slug'), # Using 'slug' as ID
                    "title": job_item.get('title'),
                    "company": job_item.get('company_name'),
                    "location": job_item.get('location'),
                    "description": job_item.get('description'), # This is HTML
                    "url": job_item.get('url'),
                    "remote": job_item.get('remote', False) # Include remote status
                    # You can add other fields like 'tags', 'job_types' if needed by frontend
                }
                jobs.append(transformed_job)
        
        print(f"Processed {len(jobs)} jobs from Arbeitnow API.")
        return jsonify({"jobs": jobs}), 200

    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
        if response is not None:
            print(f"Response content: {response.content}")
        return jsonify({"error": f"Error fetching jobs from provider: {str(http_err)}"}), response.status_code if response is not None else 500
    except requests.exceptions.RequestException as req_err:
        print(f"Request exception occurred: {req_err}")
        return jsonify({"error": f"Error connecting to job provider: {str(req_err)}"}), 503 # Service Unavailable
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return jsonify({"error": "An unexpected error occurred while searching for jobs."}), 500
