from flask import Blueprint, request, jsonify

bp = Blueprint('auth_routes', __name__, url_prefix='/auth')

@bp.route('/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
            
        email = data.get('email')
        password = data.get('password')
        
        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400
        
        # For now, simple mock authentication
        # You can integrate with your actual auth system later
        return jsonify({
            'access_token': 'mock_token_' + email.replace('@', '_'),
            'user_id': 'user_' + email.split('@')[0],
            'email': email,
            'message': 'Login successful'
        }), 200
            
    except Exception as e:
        print(f"Login error: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@bp.route('/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
            
        email = data.get('email')
        password = data.get('password')
        fullName = data.get('fullName')
        
        if not email or not password or not fullName:
            return jsonify({'error': 'Email, password, and full name are required'}), 400
        
        if len(password) < 6:
            return jsonify({'error': 'Password must be at least 6 characters'}), 400
        
        # Create user in Firebase Authentication
        try:
            from firebase_admin import auth
            user = auth.create_user(
                email=email,
                password=password,
                display_name=fullName
            )
        except auth.EmailAlreadyExistsError:
            return jsonify({'error': 'An account with this email already exists.'}), 409

        # Create user profile in Firestore
        from app.services.firebase_db import db
        profile_ref = db.collection('users').document(user.uid)
        profile_ref.set({
            'fullName': fullName,
            'email': email,
            'headline': ''
        })

        return jsonify({
            'message': 'User created successfully.',
            'uid': user.uid
        }), 201
            
    except Exception as e:
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

@bp.route('/login', methods=['GET'])
def get_login():
    return {"message": "Auth routes are working. Use POST for login."}

@bp.route('/status', methods=['GET'])
def auth_status():
    return jsonify({
        'status': 'ok',
        'message': 'Auth service is running',
        'endpoints': ['POST /auth/login', 'POST /auth/register']
    })
