"""
Routes for CV generation functionality.
"""
from flask import Blueprint, request, jsonify, send_file
import io
import json
import traceback

from app.services.latex_service import latex_service
from app.services.ai_cv_service import ai_cv_service
from app.services.latex.fixed_cv_generator import FixedCVGenerator
from app.services.latex.minimal_cv_generator import MinimalCVGenerator

# Initialize the fallback generators
fixed_cv_generator = FixedCVGenerator()
minimal_cv_generator = MinimalCVGenerator()

bp = Blueprint('cv', __name__, url_prefix='/api/cv')

@bp.route('/templates', methods=['GET'])
def get_templates():
    """Get available CV templates.
    
    Returns:
        JSON: List of available templates.
    """
    try:
        templates = latex_service.get_template_names()
        return jsonify({
            'status': 'success',
            'templates': templates
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@bp.route('/generate', methods=['POST'])
def generate_cv():
    """Generate a CV in PDF format.
    
    Request Body:
        - cv_data (dict): The CV data from the user.
        - job_description (str): The job description for tailoring CV.
        - template_name (str): The template name to use.
        - enhance_with_ai (bool): Whether to enhance the CV with AI.
        
    Returns:
        PDF: Generated CV document.
    """
    try:
        data = request.json
        cv_data = data.get('cv_data', {})
        job_description = data.get('job_description', '')
        template_name = data.get('template_name', 'modern')
        enhance_with_ai = data.get('enhance_with_ai', True)
        
        # Enhance CV with AI if requested - with error handling
        if enhance_with_ai:
            try:
                enhanced_data = ai_cv_service.enhance_cv_for_job(cv_data, job_description)
                if enhanced_data:  # Only update if we got valid data back
                    cv_data = enhanced_data
            except Exception as e:
                print(f"Error enhancing CV: {e}")
            
        # Enhance CV content with AI (but don't generate LaTeX directly)
        try:
            enhanced_data = ai_cv_service.enhance_cv_sections(cv_data, job_description)
            if enhanced_data:  # Only update if we got valid data back
                cv_data = enhanced_data
        except Exception as e:
            print(f"Error enhancing CV sections: {e}")
        
        # Generate LaTeX document
        latex_content = latex_service.generate_latex_cv(template_name, cv_data, job_description)
        
        # Debug output
        print("\nFormatted CV data for LaTeX:")
        for key, value in cv_data.items():
            preview = value[:50] if isinstance(value, str) else value
            print(f"  {key}: {preview}")
        
        # Debug first few lines of LaTeX
        print("\nFirst 10 lines of generated LaTeX:")
        for i, line in enumerate(latex_content.split('\n')[:10]):
            print(line)
        
        # Try to generate PDF from LaTeX
        try:
            pdf_content = latex_service.generate_pdf_from_latex(latex_content)
            
            # Create in-memory file-like object
            pdf_io = io.BytesIO(pdf_content)
            pdf_io.seek(0)
            
            # Send file to client
            return send_file(
                pdf_io,
                mimetype='application/pdf',
                as_attachment=True,
                download_name='generated_cv.pdf'
            )
        except Exception as e:
            print(f"Error generating PDF with standard generator: {e}")
            print("Falling back to fixed CV generator...")
            
            # Try again with the fixed generator as fallback
            try:
                print("Using fixed CV generator without AI enhancements")
                # Generate simpler LaTeX content with the fixed generator
                simple_latex = fixed_cv_generator.generate_latex_cv(template_name, cv_data)
                
                # Generate PDF from the simplified LaTeX
                pdf_content = fixed_cv_generator.generate_pdf_from_latex(simple_latex)
                
                # Create in-memory file-like object
                pdf_io = io.BytesIO(pdf_content)
                pdf_io.seek(0)
                
                # Send file to client
                return send_file(
                    pdf_io,
                    mimetype='application/pdf',
                    as_attachment=True,
                    download_name='generated_cv.pdf'
                )
            except Exception as fallback_error:
                traceback.print_exc()
                print(f"Fallback PDF generation failed: {fallback_error}")
                print("Trying minimal CV generator as last resort...")
                
                # Last resort: use the ultra-minimal generator
                try:
                    print("Using minimal CV generator as last resort")
                    # Generate PDF with the minimal generator
                    pdf_content = minimal_cv_generator.generate_pdf(cv_data)
                    
                    # Create in-memory file-like object
                    pdf_io = io.BytesIO(pdf_content)
                    pdf_io.seek(0)
                    
                    # Send file to client
                    return send_file(
                        pdf_io,
                        mimetype='application/pdf',
                        as_attachment=True,
                        download_name='generated_cv.pdf'
                    )
                except Exception as minimal_error:
                    print(f"All CV generation methods failed: {minimal_error}")
                    return jsonify({
                        'status': 'error',
                        'message': f"PDF generation failed: {str(e)}. All fallbacks failed."
                    }), 500
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@bp.route('/generate-latex', methods=['POST'])
def generate_latex():
    """Generate LaTeX code for the CV.
    
    Request Body:
        - cv_data (dict): The CV data from the user.
        - job_description (str): The job description for tailoring CV.
        - template_name (str): The template name to use.
        - enhance_with_ai (bool): Whether to enhance the CV with AI.
        
    Returns:
        JSON: Generated LaTeX content.
    """
    try:
        data = request.json
        cv_data = data.get('cv_data', {})
        job_description = data.get('job_description', '')
        template_name = data.get('template_name', 'modern')
        enhance_with_ai = data.get('enhance_with_ai', True)
        
        # Enhance CV with AI if requested - with error handling
        if enhance_with_ai:
            try:
                enhanced_data = ai_cv_service.enhance_cv_for_job(cv_data, job_description)
                if enhanced_data:
                    cv_data = enhanced_data
            except Exception as e:
                print(f"Error enhancing CV for LaTeX generation: {e}")
            
        # Format sections with proper LaTeX syntax - with error handling
        try:
            formatted_data = ai_cv_service.generate_latex_sections(cv_data, job_description)
            if formatted_data:
                cv_data = formatted_data
        except Exception as e:
            print(f"Error formatting LaTeX sections for generation: {e}")
        
        # Generate LaTeX document
        latex_content = latex_service.generate_latex_cv(template_name, cv_data, job_description)
        
        return jsonify({
            'status': 'success',
            'latex': latex_content
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500
