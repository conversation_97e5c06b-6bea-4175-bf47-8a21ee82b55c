from flask import Blueprint, request, jsonify
from app.services.firebase_db import insert_job_to_firestore, get_jobs, get_job_by_id, update_job, delete_job
from app.services.rag_job_parser import parse_offer
from app.controllers.offer_controller import parse_offer_controller
from firebase_admin import auth
from datetime import datetime, timezone

bp = Blueprint('job_routes', __name__, url_prefix='/jobs')

@bp.route('/new-application', methods=['POST'])
def new_application():
    try:
        id_token = request.headers.get('Authorization').split('Bearer ')[1]
        decoded_token = auth.verify_id_token(id_token)
        user_id = decoded_token['uid']

        job_data = request.get_json()
        job_data['userId'] = user_id
        job_data['createdAt'] = datetime.now(timezone.utc)

        required_fields = ['title', 'description', 'company', 'appliedDate']
        for field in required_fields:
            if field not in job_data:
                return jsonify({"error": f"Missing required field: {field}"}), 400

        insert_job_to_firestore(job_data)
        return jsonify({"message": "Job application added"}), 201
    except auth.InvalidIdTokenError:
        return jsonify({"error": "Invalid authorization token"}), 401
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@bp.route('/all-applications', methods=['GET'])
def get_all_applications():
    try:
        id_token = request.headers.get('Authorization').split('Bearer ')[1]
        decoded_token = auth.verify_id_token(id_token)
        user_id = decoded_token['uid']

        jobs = get_jobs(user_id)
        return jsonify({"jobs": jobs}), 200
    except auth.InvalidIdTokenError:
        return jsonify({"error": "Invalid authorization token"}), 401
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@bp.route('/<job_id>', methods=['GET'])
def get_application_by_id(job_id):
    try:
        id_token = request.headers.get('Authorization').split('Bearer ')[1]
        decoded_token = auth.verify_id_token(id_token)
        user_id = decoded_token['uid']

        job = get_job_by_id(job_id, user_id)
        return jsonify({"job": job}), 200
    except auth.InvalidIdTokenError:
        return jsonify({"error": "Invalid authorization token"}), 401
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    
@bp.route('/<job_id>', methods=['PUT'])
def update_application(job_id):
    try:
        id_token = request.headers.get('Authorization').split('Bearer ')[1]
        decoded_token = auth.verify_id_token(id_token)
        user_id = decoded_token['uid']

        updated_data = request.get_json()

        if not updated_data:
            return jsonify({"error": "No data provided"}), 400

        update_job(job_id, updated_data, user_id)
        return jsonify({"message": f"Job with ID {job_id} successfully updated"}), 200
    except auth.InvalidIdTokenError:
        return jsonify({"error": "Invalid authorization token"}), 401
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@bp.route('/<job_id>', methods=['DELETE'])
def delete_application(job_id):
    try:
        id_token = request.headers.get('Authorization').split('Bearer ')[1]
        decoded_token = auth.verify_id_token(id_token)
        user_id = decoded_token['uid']

        delete_job(job_id, user_id)
        return jsonify({"message": f"Job with ID {job_id} successfully deleted"}), 200
    except auth.InvalidIdTokenError:
        return jsonify({"error": "Invalid authorization token"}), 401
    except Exception as e:
        return jsonify({"error": str(e)}), 500
        

@bp.route('/parse-offer', methods=['POST'])
def parse_offer_route():
    return parse_offer_controller()