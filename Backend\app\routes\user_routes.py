from flask import Blueprint
from app.controllers.user_controller import get_profile_controller, update_profile_controller, change_password_controller

bp = Blueprint('user_routes', __name__)

@bp.route('/profile', methods=['GET'])
def get_profile():
    return get_profile_controller()

@bp.route('/profile', methods=['PUT'])
def update_profile():
    return update_profile_controller()

@bp.route('/profile/change-password', methods=['POST'])
def change_password():
    return change_password_controller()
