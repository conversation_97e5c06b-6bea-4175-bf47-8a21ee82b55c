"""
AI-powered CV generation service using Gemini API.
This service handles the AI integration for enhancing CV content.
"""
import os
import json
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class AICVService:
    """Service for AI-powered CV generation."""
    
    def __init__(self):
        """Initialize the AI CV service."""
        self.api_key = os.environ.get('GEMINI_API_KEY', '')
        if not self.api_key:
            print("WARNING: No GEMINI_API_KEY found in environment variables. AI enhancement disabled.")
        self.endpoint = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={self.api_key}"
        
    def enhance_cv_for_job(self, cv_data, job_description):
        """Enhance CV content based on the job description using AI.
        
        Args:
            cv_data (dict): The CV data from the user.
            job_description (str): The job description to tailor the CV to.
            
        Returns:
            dict: Enhanced CV data.
        """
        # If no API key or missing job description, return original data
        if not self.api_key or not job_description:
            print("AI enhancement skipped: Missing API key or job description")
            return cv_data
        prompt = f"""
        You are an expert CV creator specializing in tailoring CV content to job descriptions.
        Given the following CV data and job description, enhance the CV content to better match the job requirements.
        Focus on highlighting relevant skills and experiences that match the job description.

        CV Data:
        ```
        {json.dumps(cv_data, indent=2)}
        ```

        Job Description:
        ```
        {job_description}
        ```

        IMPORTANT: Return only plain text content. DO NOT include LaTeX markup, HTML, or any special formatting.
        DO NOT include formatting instructions in brackets like [this].
        Return only the enhanced CV data as a JSON object with the same structure as the input CV data.
        """

        # Prepare request to Gemini API
        request_body = {
            "contents": [
                {
                    "parts": [{"text": prompt}]
                }
            ]
        }

        try:
            response = requests.post(
                self.endpoint,
                headers={"Content-Type": "application/json"},
                json=request_body,
                timeout=30
            )
            
            if response.status_code != 200:
                raise Exception(f"API request failed with status code {response.status_code}")
            
            data = response.json()
            result = data.get('candidates', [{}])[0].get('content', {}).get('parts', [{}])[0].get('text', '')
            
            # Extract JSON from the response
            result = result.replace('```json', '').replace('```', '').strip()
            enhanced_cv_data = json.loads(result)
            
            return enhanced_cv_data
        
        except Exception as e:
            print(f"Error enhancing CV: {str(e)}")
            # Return original data if AI enhancement fails
            return cv_data
    
    def enhance_cv_sections(self, cv_data, job_description):
        """Enhance the content of CV sections based on the job description.
        
        Args:
            cv_data (dict): The CV data from the user.
            job_description (str): The job description to tailor the CV to.
            
        Returns:
            dict: Enhanced CV data with plain text content (no LaTeX).
        """
        # If no API key or missing job description, return original data
        if not self.api_key or not job_description:
            print("AI section enhancement skipped: Missing API key or job description")
            return cv_data
        prompt = f"""
        You are an expert CV content creator specializing in tailoring CV content to job descriptions.
        Given the following CV data and job description, enhance each section with better content that matches the job requirements.
        
        CV Data:
        ```
        {json.dumps(cv_data, indent=2)}
        ```

        Job Description:
        ```
        {job_description}
        ```

        Enhance the following sections with better PLAIN TEXT content:
        - experience: Improve each experience item to highlight relevant skills for the job description
        - education: Enhance education details to emphasize relevant coursework or achievements
        - skills: Expand the skills list with relevant skills from the job description
        - summary: Write a compelling professional summary tailored to the job

        IMPORTANT INSTRUCTIONS:
        1. Return only plain text content
        2. DO NOT include any LaTeX markup like \begin{{itemize}}, \item, etc.
        3. DO NOT include HTML formatting
        4. DO NOT include formatting instructions in brackets like [this]
        5. DO NOT include placeholders
        6. Return the enhanced CV data as a JSON object with the same structure as the input

        The LaTeX formatting will be handled separately after your response.
        """

        # Prepare request to Gemini API
        request_body = {
            "contents": [
                {
                    "parts": [{"text": prompt}]
                }
            ]
        }

        try:
            response = requests.post(
                self.endpoint,
                headers={"Content-Type": "application/json"},
                json=request_body,
                timeout=30
            )
            
            if response.status_code != 200:
                raise Exception(f"API request failed with status code {response.status_code}")
            
            data = response.json()
            result = data.get('candidates', [{}])[0].get('content', {}).get('parts', [{}])[0].get('text', '')
            
            # Extract JSON from the response
            result = result.replace('```json', '').replace('```', '').strip()
            enhanced_data = json.loads(result)
            
            return enhanced_data
        
        except Exception as e:
            print(f"Error enhancing CV sections: {str(e)}")
            # Return original data if AI formatting fails
            return cv_data

# Create a singleton instance
ai_cv_service = AICVService()
