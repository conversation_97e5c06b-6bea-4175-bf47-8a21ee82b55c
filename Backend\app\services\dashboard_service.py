import datetime
from firebase_admin import firestore
import logging

def get_dashboard_stats(user_id, period):
    """
    Calculates job application statistics for a given user and period.
    """
    try:
        db = firestore.client()
        job_applications_ref = db.collection('jobApplications').where('userId', '==', user_id)

        # Filter by period if specified
        if period != 'all':
            try:
                days = int(period)
                start_date = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=days)
                # Assuming 'createdAt' field exists and is a Firestore Timestamp
                job_applications_ref = job_applications_ref.where('createdAt', '>=', start_date)
            except ValueError:
                # Handle cases where period is not a valid number
                logging.warning(f"Invalid period value received: {period}. Defaulting to 'all'.")
                pass

        applications = job_applications_ref.stream()

        status_counts = {
            'pending': 0,
            'interviewing': 0,
            'offered': 0,
            'rejected': 0,
            'saved': 0
        }
        total_applications = 0

        for app in applications:
            app_data = app.to_dict()
            status = app_data.get('status', 'saved').lower()
            if status in status_counts:
                status_counts[status] += 1
            total_applications += 1

        pie_chart_data = {
            "labels": list(status_counts.keys()),
            "datasets": [{
                "data": list(status_counts.values()),
                "backgroundColor": [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF'
                ]
            }]
        }

        return {
            "totalApplications": total_applications,
            "statusCounts": status_counts,
            "pieChartData": pie_chart_data
        }
    except Exception as e:
        logging.error(f"Error in get_dashboard_stats for user {user_id}: {e}")
        return {
            "totalApplications": 0,
            "statusCounts": {},
            "pieChartData": {"labels": [], "datasets": []}
        }

def get_upcoming_reminders(user_id):
    """
    Fetches upcoming reminders for a given user.
    """
    try:
        db = firestore.client()
        now = datetime.datetime.now(datetime.timezone.utc)

        reminders_ref = db.collection('jobApplications') \
            .where('userId', '==', user_id) \
            .where('reminderDate', '>=', now) \
            .where('reminderSent', '==', False) \
            .order_by('reminderDate', direction=firestore.Query.ASCENDING) \
            .limit(5)

        reminders = []
        for reminder_doc in reminders_ref.stream():
            reminder_data = reminder_doc.to_dict()
            reminder_date = reminder_data.get('reminderDate')
            if isinstance(reminder_date, datetime.datetime):
                 reminder_date_str = reminder_date.isoformat()
            else:
                 reminder_date_str = str(reminder_date)

            reminders.append({
                "id": reminder_doc.id,
                "jobTitle": reminder_data.get('title', 'N/A'),
                "company": reminder_data.get('company', 'N/A'),
                "reminderDate": reminder_date_str
            })

        return reminders
    except Exception as e:
        logging.error(f"Error in get_upcoming_reminders for user {user_id}: {e}")
        return []
