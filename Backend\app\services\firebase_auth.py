import firebase_admin
from firebase_admin import auth, credentials
import requests
import json
import os

class FirebaseAuth:
    def __init__(self):
        self.api_key = None
        self.initialize_firebase()
        
    def initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        try:
            # Check if Firebase app is already initialized
            if not firebase_admin._apps:
                # Path to service account key
                service_account_path = os.path.join(
                    os.path.dirname(__file__), 
                    '..', 'config', 'firebase', 'serviceAccountKey.json'
                )
                
                if os.path.exists(service_account_path):
                    cred = credentials.Certificate(service_account_path)
                    firebase_admin.initialize_app(cred)
                    print("Firebase Admin SDK initialized successfully")
                    
                    # Try to get API key from service account file
                    try:
                        with open(service_account_path, 'r') as f:
                            service_account_data = json.load(f)
                            # You'll need to set the Web API key separately
                            # This is typically found in your Firebase project settings
                            self.api_key = os.getenv('FIREBASE_WEB_API_KEY')
                            if not self.api_key:
                                print("Warning: FIREBASE_WEB_API_KEY not set in environment")
                    except Exception as e:
                        print(f"Warning: Could not load API key: {e}")
                else:
                    print(f"Warning: Service account file not found at {service_account_path}")
            else:
                print("Firebase Admin SDK already initialized")
                
        except Exception as e:
            print(f"Firebase initialization error: {e}")
    
    def create_user(self, email, password):
        """Create a new user with email and password"""
        try:
            # Create user with Admin SDK
            user_record = auth.create_user(
                email=email,
                password=password,
                email_verified=False
            )
            
            # Generate custom token
            custom_token = auth.create_custom_token(user_record.uid)
            
            # Exchange custom token for ID token using Firebase Auth REST API
            id_token = self._exchange_custom_token(custom_token.decode('utf-8'))
            
            return {
                'success': True,
                'user_id': user_record.uid,
                'id_token': id_token,
                'email': email
            }
            
        except auth.EmailAlreadyExistsError:
            return {
                'success': False,
                'error': 'Email already exists'
            }
        except Exception as e:
            print(f"Create user error: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def sign_in(self, email, password):
        """Sign in user with email and password"""
        try:
            # Get user by email
            user_record = auth.get_user_by_email(email)
            
            # Since we can't verify password with Admin SDK, we use Firebase Auth REST API
            id_token = self._sign_in_with_password(email, password)
            
            if id_token:
                return {
                    'success': True,
                    'user_id': user_record.uid,
                    'id_token': id_token,
                    'email': email
                }
            else:
                return {
                    'success': False,
                    'error': 'Invalid credentials'
                }
                
        except auth.UserNotFoundError:
            return {
                'success': False,
                'error': 'User not found'
            }
        except Exception as e:
            print(f"Sign in error: {e}")
            return {
                'success': False,
                'error': 'Authentication failed'
            }
    
    def verify_token(self, id_token):
        """Verify Firebase ID token"""
        try:
            decoded_token = auth.verify_id_token(id_token)
            return {
                'success': True,
                'user_id': decoded_token['uid'],
                'email': decoded_token.get('email')
            }
        except Exception as e:
            print(f"Token verification error: {e}")
            return {
                'success': False,
                'error': 'Invalid token'
            }
    
    def _sign_in_with_password(self, email, password):
        """Sign in using Firebase Auth REST API"""
        if not self.api_key:
            print("Firebase Web API key not configured")
            return None
            
        try:
            url = f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key={self.api_key}"
            
            payload = {
                "email": email,
                "password": password,
                "returnSecureToken": True
            }
            
            response = requests.post(url, json=payload)
            
            if response.status_code == 200:
                data = response.json()
                return data['idToken']
            else:
                print(f"Sign in API error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Sign in API error: {e}")
            return None
    
    def _exchange_custom_token(self, custom_token):
        """Exchange custom token for ID token"""
        if not self.api_key:
            print("Firebase Web API key not configured")
            return None
            
        try:
            url = f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key={self.api_key}"
            
            payload = {
                "token": custom_token,
                "returnSecureToken": True
            }
            
            response = requests.post(url, json=payload)
            
            if response.status_code == 200:
                data = response.json()
                return data['idToken']
            else:
                print(f"Custom token exchange error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Custom token exchange error: {e}")
            return None