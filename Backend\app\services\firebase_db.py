import os
from dotenv import load_dotenv
import firebase_admin
from firebase_admin import credentials, firestore
from google.cloud.firestore_v1.base_query import FieldFilter

# Load environment variables from .env file
load_dotenv()

# Retrieve the path to the service account key from environment variables
service_account_key = os.getenv("FIREBASE_SERVICE_ACCOUNT_KEY")

if not service_account_key:
    raise Exception("Firebase service account key path is not set in the .env file.")

# Initialize Firebase only once using the service account key
if not firebase_admin._apps:
    cred = credentials.Certificate(service_account_key)
    firebase_admin.initialize_app(cred)

# Initialize Firestore client
db = firestore.client()

def insert_job_to_firestore(job_data: dict):
    try:
        # Add a new document in the "jobs" collection
        db.collection("jobs").add(job_data)
    except Exception as e:
        raise Exception(f"Error inserting job data: {str(e)}")

def get_jobs(user_id: str):
    try:
        # Get all jobs for a specific user from the "jobApplications" collection
        jobs_ref = db.collection("jobs").where(filter=FieldFilter("userId", "==", user_id))
        docs = jobs_ref.stream()
        
        job_list = []
        for doc in docs:
            job_data = doc.to_dict()
            job_data["id"] = doc.id
            job_list.append(job_data)
        
        return job_list

    except Exception as e:
        raise Exception(f"Error retrieving job data: {str(e)}")

def get_job_by_id(job_id: str, user_id: str):
    try:
        doc_ref = db.collection("jobs").document(job_id)
        doc = doc_ref.get()
        
        if doc.exists:
            job_data = doc.to_dict()
            if job_data.get("userId") != user_id:
                raise Exception("User not authorized to access this job.")
            job_data["id"] = doc.id
            return job_data
        else:
            raise Exception(f"Job with ID {job_id} not found.")
    
    except Exception as e:
        raise Exception(f"Error retrieving job by ID: {str(e)}")

def update_job(job_id: str, updated_data: dict, user_id: str):
    try:
        doc_ref = db.collection("jobs").document(job_id)
        doc = doc_ref.get()
        if not doc.exists:
            raise Exception(f"Job with ID {job_id} not found.")
        
        job_data = doc.to_dict()
        if job_data.get("userId") != user_id:
            raise Exception("User not authorized to update this job.")
            
        doc_ref.update(updated_data)
        return f"Job with ID {job_id} successfully updated."
    except Exception as e:
        raise Exception(f"Error updating job: {str(e)}")

def delete_job(job_id: str, user_id: str):
    try:
        doc_ref = db.collection("jobs").document(job_id)
        doc = doc_ref.get()
        if not doc.exists:
            raise Exception(f"Job with ID {job_id} not found.")
            
        job_data = doc.to_dict()
        if job_data.get("userId") != user_id:
            raise Exception("User not authorized to delete this job.")

        doc_ref.delete()
        return f"Job with ID {job_id} successfully deleted."
    except Exception as e:
        raise Exception(f"Error deleting job: {str(e)}")