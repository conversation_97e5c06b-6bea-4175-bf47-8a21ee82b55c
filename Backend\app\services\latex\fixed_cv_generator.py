from jinja2 import Environment, FileSystemLoader
import os
import subprocess
import tempfile

class FixedCVGenerator:
    """Simple CV generator for reliable PDF generation."""

    def __init__(self):
        """Initialize the fixed CV generator."""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        template_dir = os.path.abspath(os.path.join(current_dir, '../../templates/latex'))

        self.env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True
        )

    def generate_latex_cv(self, template_name, cv_data):
        """Generate a LaTeX CV document with minimal processing."""
        clean_data = self._prepare_clean_data(cv_data)

        try:
            template = self.env.get_template(f"{template_name}.tex")
            return template.render(**clean_data)
        except Exception as e:
            print(f"Template rendering error: {e}")
            return self._generate_minimal_latex(clean_data)

    def _prepare_clean_data(self, cv_data):
        clean_data = {
            'name': self._simple_escape(cv_data.get('name', '')),
            'email': self._simple_escape(cv_data.get('email', '')),
            'phone': self._simple_escape(cv_data.get('phone', '')),
            'website': self._format_url(cv_data.get('website', '')),
            'linkedin': self._format_url(cv_data.get('linkedin', ''), is_linkedin=True),
            'summary': self._simple_escape(cv_data.get('summary', ''))
        }

        for section in ['education', 'experience', 'skills', 'projects', 'publications']:
            clean_data[section] = self._format_simple_itemize(cv_data.get(section, []))

        return clean_data

    def _simple_escape(self, text):
        if not text:
            return ''
        text = str(text)
        escapes = {
            '&': '\\&', '%': '\\%', '$': '\\$', '#': '\\#',
            '_': '\\_', '{': '\\{', '}': '\\}', '~': '\\textasciitilde{}',
            '^': '\\textasciicircum{}', '\\': '\\textbackslash{}',
        }
        for char, repl in escapes.items():
            text = text.replace(char, repl)
        return text

    def _format_url(self, url, is_linkedin=False):
        if not url:
            return ''
        if not url.startswith(('http://', 'https://')):
            if is_linkedin:
                return f'https://linkedin.com/in/{url}'
            return f'https://{url}'
        return url

    def _format_simple_itemize(self, data):
        if not data:
            return ''
        result = '\\begin{itemize}\n'
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict) and 'description' in item:
                    desc = item['description']
                    if isinstance(desc, str):
                        result += f'\\item {self._simple_escape(desc)}\n'
                    elif isinstance(desc, list):
                        for point in desc:
                            result += f'\\item {self._simple_escape(point)}\n'
                elif isinstance(item, str):
                    result += f'\\item {self._simple_escape(item)}\n'
        elif isinstance(data, str):
            result += f'\\item {self._simple_escape(data)}\n'
        result += '\\end{itemize}\n'
        return result

    def _generate_minimal_latex(self, data):
        return f"""\\documentclass[11pt,a4paper]{{article}}
            \\usepackage[utf8]{{inputenc}}
            \\usepackage{{geometry}}
            \\usepackage{{hyperref}}

            \\geometry{{left=2cm, right=2cm, top=2cm, bottom=2cm}}
            \\begin{{document}}

            \\begin{{center}}
            \\huge\\textbf{{{data['name']}}}\\\\
            \\normalsize
            {data['email']} | {data['phone']}
            \\end{{center}}

            \\section*{{Summary}}
            {data['summary']}

            \\section*{{Education}}
            {data['education']}

            \\section*{{Experience}}
            {data['experience']}

            \\section*{{Skills}}
            {data['skills']}

            \\section*{{Projects}}
            {data['projects']}

            \\end{{document}}
            """

    def generate_pdf_from_latex(self, latex_content, timeout=60):
        with tempfile.TemporaryDirectory() as temp_dir:
            tex_path = os.path.join(temp_dir, 'cv.tex')
            with open(tex_path, 'w', encoding='utf-8') as tex_file:
                tex_file.write(latex_content)

            try:
                result = subprocess.run(
                    ['pdflatex', '-interaction=nonstopmode', 'cv.tex'],
                    cwd=temp_dir,
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )

                if result.returncode != 0:
                    print("LaTeX compilation failed:")
                    print(result.stdout)
                    print(result.stderr)
                    raise Exception("PDF generation failed due to LaTeX error")

                pdf_path = os.path.join(temp_dir, 'cv.pdf')
                with open(pdf_path, 'rb') as pdf_file:
                    return pdf_file.read()

            except subprocess.TimeoutExpired:
                print("PDF generation timed out after", timeout, "seconds")
                raise Exception("PDF generation timed out")

            except Exception as e:
                print(f"Error generating PDF: {e}")
                raise
