"""
LaTeX Formatter Module
Handles formatting of CV data into LaTeX-friendly structures.
"""
import re
from .latex_renderer import LaTeXRenderer

class LaTeXFormatter:
    """Formats CV data for LaTeX rendering."""
    
    def __init__(self):
        """Initialize the LaTeX formatter."""
        self.renderer = LaTeXRenderer()
    
    def format_cv_data_for_latex(self, cv_data, job_description=None):
        """Format CV data for LaTeX rendering.
        
        Args:
            cv_data (dict): The CV data from the user.
            job_description (str, optional): Job description for tailoring.
            
        Returns:
            dict: LaTeX-formatted CV data.
        """
        formatted_data = {}
        
        # Basic contact info
        formatted_data['name'] = self._latex_escape(cv_data.get('name', ''))
        formatted_data['email'] = self._latex_escape(cv_data.get('email', ''))
        formatted_data['phone'] = self._latex_escape(cv_data.get('phone', ''))
        
        # Website/LinkedIn - format as hyperlinks
        website = cv_data.get('website', '')
        if website:
            # Check if already formatted as href by AI
            if isinstance(website, str) and ('\\href' in website):
                formatted_data['website'] = self._sanitize_ai_latex(website)
            else:
                formatted_data['website'] = self._format_href(website)
        else:
            formatted_data['website'] = ''
        
        linkedin = cv_data.get('linkedin', '')
        if linkedin:
            # Check if already formatted as href by AI
            if isinstance(linkedin, str) and ('\\href' in linkedin):
                formatted_data['linkedin'] = self._sanitize_ai_latex(linkedin)
            else:
                formatted_data['linkedin'] = self._format_href(linkedin)
        else:
            formatted_data['linkedin'] = ''
            
        # Summary section
        summary = cv_data.get('summary', '')
        if summary:
            formatted_data['summary'] = self._clean_text_content(summary)
        else:
            formatted_data['summary'] = ''
        
        # Education
        education = cv_data.get('education', [])
        if education:
            formatted_data['education'] = self._format_section_items(education)
        
        # Experience
        experience = cv_data.get('experience', [])
        if experience:
            formatted_data['experience'] = self._format_section_items(experience)
        
        # Skills - format as a list
        skills = cv_data.get('skills', [])
        if skills:
            formatted_data['skills'] = self._format_skills(skills)
        
        # Projects
        projects = cv_data.get('projects', [])
        if projects:
            formatted_data['projects'] = self._format_section_items(projects)
        
        # Publications
        publications = cv_data.get('publications', [])
        if publications:
            formatted_data['publications'] = self._format_section_items(publications)
        
        # Languages
        languages = cv_data.get('languages', [])
        if languages:
            formatted_data['languages'] = self._format_skills(languages)
            
        # Certifications
        certifications = cv_data.get('certifications', [])
        if certifications:
            formatted_data['certifications'] = self._format_section_items(certifications)
        
        return formatted_data
    
    def _sanitize_ai_latex(self, content):
        """Sanitize LaTeX content generated by AI to prevent compilation issues.
        
        Args:
            content (str): AI-generated LaTeX content.
            
        Returns:
            str: Sanitized LaTeX content.
        """
        if not content:
            return ""
        
        # Aggressive cleaning of all instructions and placeholders
        
        # Remove any bracketed instructions [like this] that the AI might have included
        content = re.sub(r'\\item\s*\[.*?\]', '\\item', content)
        
        # Fix malformed itemize environments
        if '\\begin{itemize}' in content and '\\end{itemize}' not in content:
            content += '\n\\end{itemize}'
        
        # Remove all square bracket content (AI instructions and placeholders)
        content = re.sub(r'\[.*?\]', '', content)
        
        # Remove common AI instruction patterns
        content = re.sub(r'\[[^\]]*\]', '', content)  # Catch any remaining [brackets]
        content = re.sub(r'\(e\.g\.,[^\)]*\)', '', content)  # Remove examples in parentheses
        
        # Remove markdown-style comments
        content = re.sub(r'<!--.*?-->', '', content)
        
        # Remove instructional patterns (common in AI outputs)
        content = re.sub(r'mention [^.,]*', '', content)
        content = re.sub(r'add [^.,]*', '', content)
        content = re.sub(r'insert [^.,]*', '', content)
        content = re.sub(r'describe [^.,]*', '', content)
        content = re.sub(r'list [^.,]*', '', content)
        
        # Clean up any texttt tags that might reference placeholders
        content = re.sub(r'\\texttt\{[^}]*\}', '', content)
        
        # Remove empty items
        content = re.sub(r'\\item\s*$', '', content, flags=re.MULTILINE)
        content = re.sub(r'\\item\s*\\item', '\\item', content)
        
        # Fix potential recursive issues
        content = content.replace('\\begin{itemize}\\begin{itemize}', '\\begin{itemize}')
        content = content.replace('\\end{itemize}\\end{itemize}', '\\end{itemize}')
        
        # Clean up whitespace
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r'\\item\s+', '\\item ', content)
        
        return content.strip()
    
    def _format_href(self, url):
        """Format URL as a LaTeX href.
        
        Args:
            url (str): The URL to format.
            
        Returns:
            str: LaTeX href.
        """
        # Clean the URL first
        url = url.strip()
        
        # Make sure URL has http/https prefix
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        # Format with href
        display = url.replace('https://', '').replace('http://', '')
        display = display.rstrip('/')  # Remove trailing slash for display
        
        # Use minimal escaping since this already contains LaTeX commands
        safe_url = self.renderer._minimal_latex_escape(url)
        safe_display = self.renderer._minimal_latex_escape(display)
        
        return f"\\href{{{safe_url}}}{{{safe_display}}}"
    
    def _clean_text_content(self, text):
        """Clean and prepare text content for LaTeX.
        
        Args:
            text (str): Raw text content.
            
        Returns:
            str: Cleaned LaTeX-safe text.
        """
        if not text:
            return ''
            
        text = str(text)
        
        # Handle AI-generated content with LaTeX commands already in it
        if '\\' in text:
            text = self._sanitize_ai_latex(text)
            return text
            
        # Remove excess whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Fix common dash issues
        text = text.replace('--', '–')
        text = text.replace('—', '–')
        
        # Escape LaTeX special characters
        return self.renderer._latex_escape(text)
    
    def _format_section_items(self, section_data):
        """Format a list of section items for LaTeX.
        
        Args:
            section_data (list or str): Section data to format.
            
        Returns:
            str: LaTeX-formatted section content.
        """
        if not section_data:
            return ""
        
        # If it's already a formatted string with LaTeX, sanitize it
        if isinstance(section_data, str) and "\\begin{itemize}" in section_data:
            return self._sanitize_ai_latex(section_data)
            
        # Handle AI-generated list with formatted content
        if isinstance(section_data, list) and len(section_data) == 1 and isinstance(section_data[0], dict) and 'description' in section_data[0]:
            desc = section_data[0]['description']
            if isinstance(desc, str) and '\\begin{itemize}' in desc:
                return self._sanitize_ai_latex(desc)
        
        # Format structured data items
        if isinstance(section_data, list):
            result = "\\begin{itemize}\n"
            
            for item in section_data:
                if isinstance(item, dict):
                    # Format structured entry (e.g., education/experience)
                    entry_parts = []
                    
                    # Title/role/degree with optional institution/company
                    if 'title' in item or 'role' in item or 'degree' in item:
                        title = item.get('title', item.get('role', item.get('degree', '')))
                        org = item.get('institution', item.get('company', item.get('organization', '')))
                        
                        if org:
                            entry_parts.append(f"\\textbf{{{self._clean_text_content(title)}}} at \\textit{{{self._clean_text_content(org)}}}")
                        else:
                            entry_parts.append(f"\\textbf{{{self._clean_text_content(title)}}}")
                    
                    # Date range
                    if 'start_date' in item or 'end_date' in item:
                        start = item.get('start_date', '')
                        end = item.get('end_date', 'Present')
                        if start and end:
                            entry_parts.append(f"({self._clean_text_content(start)} - {self._clean_text_content(end)})")
                    
                    # Description - handle potential LaTeX content
                    if 'description' in item:
                        desc = item['description']
                        if isinstance(desc, list):
                            # Format as a paragraph with bullet points
                            desc_text = "\\begin{itemize}\n"
                            for point in desc:
                                desc_text += f"\\item {self._clean_text_content(point)}\n"
                            desc_text += "\\end{itemize}\n"
                            entry_parts.append(desc_text)
                        else:
                            # Single paragraph - may contain LaTeX
                            if '\\begin{itemize}' in desc:
                                entry_parts.append(self._sanitize_ai_latex(desc))
                            else:
                                entry_parts.append(self._clean_text_content(desc))
                    
                    # Combine parts with appropriate spacing
                    result += f"\\item {' '.join(entry_parts)}\n"
                else:
                    # Simple string item
                    result += f"\\item {self._clean_text_content(item)}\n"
            
            result += "\\end{itemize}\n"
            return result
        else:
            # Single string for the entire section
            return self._clean_text_content(section_data)
    
    def _format_skills(self, skills):
        """Format skills list for LaTeX.
        
        Args:
            skills (list): List of skill names.
            
        Returns:
            str: LaTeX-formatted skills content.
        """
        if not skills:
            return ""
            
        # AI-generated skills with LaTeX markup
        if isinstance(skills, list) and len(skills) >= 1:
            # Check for AI-generated pre-formatted content
            if any(isinstance(s, str) and '\\begin{itemize}' in s for s in skills):
                # Join all parts and sanitize
                combined = '\n'.join([s for s in skills if isinstance(s, str)])
                return self._sanitize_ai_latex(combined)
        
        # Simple list of skills
        if isinstance(skills, list):
            # First aggressively filter out placeholder skills
            filtered_skills = []
            for skill in skills:
                if not skill or not isinstance(skill, str):
                    continue
                
                # Skip skills that are likely placeholders
                if any(pattern in skill.lower() for pattern in ['[', ']', 'directly mentioned', 'job description', 'add', 'insert', 'mention']):
                    continue
                    
                # Remove any remaining instructions/placeholders
                skill = re.sub(r'\[.*?\]', '', skill).strip()
                
                if skill:  # Only add non-empty skills
                    filtered_skills.append(skill)
            
            # Only include non-empty skills
            if filtered_skills:
                content = "\\begin{itemize}\n"
                for skill in filtered_skills:
                    content += f"\\item {self._clean_text_content(skill)}\n"
                content += "\\end{itemize}\n"
                return content
            else:
                # If all skills were filtered out, return empty string
                return ""
                
        elif isinstance(skills, str):
            # Single string with all skills - sanitize and clean
            clean_skills = self._sanitize_ai_latex(skills)
            if clean_skills.strip():
                return clean_skills
            return ""
    
    def _latex_escape(self, text):
        """Wrapper for the LaTeX renderer's escape method.
        
        Args:
            text: Text to escape.
            
        Returns:
            str: Escaped text.
        """
        return self.renderer._latex_escape(text)
