"""
LaTeX Renderer Module
Handles rendering of LaTeX templates and proper escaping of special characters.
"""
import os
import jinja2
from pathlib import Path
from jinja2 import Environment, FileSystemLoader

class LaTeXRenderer:
    """Renders LaTeX templates with proper escaping and fallback mechanisms."""
    
    def __init__(self, templates_dir=None):
        """Initialize the LaTeX renderer.
        
        Args:
            templates_dir (Path, optional): Directory containing LaTeX templates.
        """
        if templates_dir is None:
            self.templates_dir = Path(__file__).parent.parent.parent / "templates" / "latex"
        else:
            self.templates_dir = Path(templates_dir)
            
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        self.env = Environment(loader=FileSystemLoader(str(self.templates_dir)))
    
    def get_template_content(self, template_name):
        """Get the content of a template by name.
        
        Args:
            template_name (str): Name of the template to load.
            
        Returns:
            str: Content of the template.
            
        Raises:
            FileNotFoundError: If template doesn't exist.
        """
        template_path = self.templates_dir / f"{template_name}.tex"
        if not template_path.exists():
            raise FileNotFoundError(f"Template {template_name} not found at {template_path}")
            
        with open(template_path, "r", encoding="utf-8") as f:
            return f.read()
    
    def render_template(self, template_content, data):
        """Render a LaTeX template with the provided data.
        
        Args:
            template_content (str): LaTeX template content.
            data (dict): Data to render in the template.
            
        Returns:
            str: Rendered LaTeX content.
            
        Raises:
            Exception: If rendering fails.
        """
        try:
            # Use Jinja2 with better error handling
            env = jinja2.Environment(
                autoescape=False, 
                trim_blocks=True, 
                lstrip_blocks=True,
                undefined=jinja2.DebugUndefined,  # Better error messages
                keep_trailing_newline=True
            )
            template = env.from_string(template_content)
            
            # Render template with data
            return template.render(**data)
        except Exception as template_error:
            print(f"Template rendering error: {template_error}, trying manual approach")
            
            # Fallback to manual template substitution
            latex_content = template_content
            
            # Simple manual variable replacement
            for key, value in data.items():
                placeholder = f"{{{{{key}}}}}"
                if isinstance(value, str):
                    latex_content = latex_content.replace(placeholder, value)
                elif value:  # For non-empty lists or dicts
                    # For sections that might have structured content
                    if key in ['experience', 'education', 'skills', 'projects', 'publications']:
                        # For these sections we keep the section but replace with formatted content
                        section_start = f"\\section{{{key.capitalize()}}}"
                        next_section = latex_content.find("\\section", latex_content.find(section_start) + len(section_start))
                        if next_section > -1:
                            # Insert our content after this section header but before the next section
                            insert_pos = latex_content.find("\n", latex_content.find(section_start)) + 1
                            if isinstance(value, str):
                                replacement = value
                            else:
                                # Format it as an itemize list
                                replacement = "\\begin{itemize}\n"
                                if isinstance(value, list):
                                    for item in value:
                                        replacement += f"\\item {item}\n"
                                replacement += "\\end{itemize}\n"
                                
                            latex_content = latex_content[:insert_pos] + replacement + latex_content[insert_pos:]
                else:
                    # Remove empty sections
                    latex_content = self._remove_section(latex_content, key.capitalize())
                    
            return latex_content
    
    def _remove_section(self, content, section_name):
        """Remove a section from LaTeX content if it's empty.
        
        Args:
            content (str): The LaTeX content.
            section_name (str): The name of the section to remove.
            
        Returns:
            str: The LaTeX content with the section removed.
        """
        section_start = f"\\section{{{section_name}}}"
        section_end = "\\section"
        
        if section_start in content:
            start_pos = content.find(section_start)
            # Find the next section
            end_pos = content.find(section_end, start_pos + len(section_start))
            
            if end_pos > start_pos:
                # Remove until the next section
                return content[:start_pos] + content[end_pos:]
            else:
                # This is the last section, remove to the end
                return content[:start_pos]
        
        return content
    
    def _latex_escape(self, text):
        """Escape special LaTeX characters in text.
        
        Args:
            text: Text to escape.
            
        Returns:
            str: Escaped text.
        """
        if not text:
            return ''
            
        text = str(text)
        # Check if text already contains LaTeX commands
        has_latex_commands = '\\' in text and ('{' in text or '}' in text)
        
        if has_latex_commands:
            # Only do minimal escaping for text with LaTeX commands
            return self._minimal_latex_escape(text)
        else:
            # Full escaping for regular text
            replacements = [
                ('\\', r'\textbackslash{}'),
                ('&', r'\&'),
                ('%', r'\%'),
                ('$', r'\$'),
                ('#', r'\#'),
                ('_', r'\_'),
                ('{', r'\{'),
                ('}', r'\}'),
                ('~', r'\textasciitilde{}'),
                ('^', r'\textasciicircum{}'),
            ]
            
            result = text
            for char, escaped in replacements:
                result = result.replace(char, escaped)
            
            return result
    
    def _minimal_latex_escape(self, text):
        """Minimal LaTeX escaping for text that already contains LaTeX commands.
        
        Args:
            text: Text to escape.
            
        Returns:
            str: Minimally escaped text.
        """
        if not text:
            return ''
            
        text = str(text)
        
        # Only escape specific characters that would cause LaTeX errors
        replacements = [
            ('&', r'\&'),
            ('%', r'\%'),
            ('$', r'\$'),
            ('#', r'\#'),
            ('_', r'\_'),
        ]
        
        result = text
        for char, escaped in replacements:
            result = result.replace(char, escaped)
            
        return result
