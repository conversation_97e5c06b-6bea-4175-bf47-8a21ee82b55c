"""
LaTeX Service Module
Main service class that integrates renderer, formatter and PDF generator.
"""
import os
from pathlib import Path
from .latex_renderer import LaTeX<PERSON><PERSON><PERSON>
from .latex_formatter import La<PERSON>e<PERSON><PERSON><PERSON>atter
from .pdf_generator import PDFGenerator

class LaTeXService:
    """Service for generating LaTeX CVs."""
    
    def __init__(self):
        """Initialize the LaTeX service."""
        # Initialize components
        self.templates_dir = Path(__file__).parent.parent.parent / "templates" / "latex"
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        self.renderer = LaTeXRenderer(self.templates_dir)
        self.formatter = LaTeXFormatter()
        self.pdf_generator = PDFGenerator()
        
        # Create default template if it doesn't exist
        self._create_default_templates()
    
    def check_latex_installation(self):
        """Check if LaTeX is properly installed."""
        return self.pdf_generator.check_latex_installation()
    
    def _create_default_templates(self):
        """Create default templates if they don't exist."""
        os.makedirs(self.templates_dir, exist_ok=True)
        
        # Set up template paths
        templates = {
            "modern_cv.tex": self._get_modern_cv_template(),
            "academic_cv.tex": self._get_academic_cv_template(),
            "classic_cv.tex": self._get_classic_cv_template()
        }
        
        # Create templates if they don't exist
        for filename, content in templates.items():
            template_path = self.templates_dir / filename
            if not template_path.exists():
                with open(template_path, "w", encoding="utf-8") as f:
                    f.write(content)
                    
    def get_available_templates(self):
        """Get a list of available templates.
        
        Returns:
            list: List of template names without extension.
        """
        templates = []
        for file in self.templates_dir.glob("*.tex"):
            templates.append(file.stem)
        return templates
    
    def get_template_names(self):
        """Get a list of available template names (for backward compatibility).
        
        Returns:
            list: List of template names without extension.
        """
        return self.get_available_templates()
    
    def get_template_content(self, template_name):
        """Get the content of a template.
        
        Args:
            template_name (str): Template name without extension.
            
        Returns:
            str: Template content.
        """
        return self.renderer.get_template_content(template_name)
    
    def format_cv_data_for_latex(self, cv_data, job_description=None):
        """Format CV data for LaTeX.
        
        Args:
            cv_data (dict): CV data to format.
            job_description (str, optional): Job description for tailoring.
            
        Returns:
            dict: Formatted data ready for LaTeX rendering.
        """
        return self.formatter.format_cv_data_for_latex(cv_data, job_description)
    
    def generate_latex_cv(self, template_name, cv_data, job_description=None):
        """Generate a LaTeX CV document.
        
        Args:
            template_name (str): The template name to use.
            cv_data (dict): The CV data from the user.
            job_description (str, optional): The job description for tailoring CV.
        
        Returns:
            str: LaTeX document content.
        
        Raises:
            Exception: If generation fails.
        """
        try:
            # Get template content with validation
            template_content = self.get_template_content(template_name)
            
            # Format CV data
            formatted_data = self.format_cv_data_for_latex(cv_data, job_description)
            
            # Debug formatted data
            print("\nFormatted CV data for LaTeX:")
            for key, value in formatted_data.items():
                if key in ['name', 'email', 'phone', 'website', 'linkedin']:
                    print(f"  {key}: {value}")
                else:
                    preview = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                    print(f"  {key}: {preview}")
            
            # Pre-process data to avoid template rendering issues
            safe_data = {}
            for key, value in formatted_data.items():
                if isinstance(value, str):
                    # Fix problematic LaTeX sequences that break template rendering
                    # The main issue is with sequences like \textbackslash\{\} that confuse Jinja2
                    value = value.replace('\\textbackslash\\{\\}', '\\')
                    value = value.replace('\\textbackslash{}', '\\')
                    value = value.replace('\\{', '{')
                    value = value.replace('\\}', '}')
                safe_data[key] = value
            
            # Render the template
            latex_content = self.renderer.render_template(template_content, safe_data)
            
            # Debug first few lines of the generated LaTeX
            print("\nFirst 10 lines of generated LaTeX:")
            print("\n".join(latex_content.split("\n")[:10]))
            
            # Optionally save to a file for debugging
            if os.environ.get("DEBUG_LATEX"):
                with open(f"debug_cv_{template_name}.tex", "w", encoding="utf-8") as f:
                    f.write(latex_content)
            
            return latex_content
            
        except Exception as e:
            print(f"Error generating LaTeX CV: {str(e)}")
            raise e
    
    def generate_pdf_cv(self, template_name, cv_data, job_description=None):
        """Generate a PDF CV document.
        
        Args:
            template_name (str): The template name to use.
            cv_data (dict): The CV data from the user.
            job_description (str, optional): The job description for tailoring CV.
        
        Returns:
            bytes: PDF document as bytes.
        
        Raises:
            Exception: If generation fails.
        """
        # Generate LaTeX first
        latex_content = self.generate_latex_cv(template_name, cv_data, job_description)
        
        # Generate PDF from LaTeX
        return self.pdf_generator.generate_pdf_from_latex(latex_content)
        
    def generate_pdf_from_latex(self, latex_content):
        """Generate a PDF from LaTeX content (for backward compatibility).
        
        Args:
            latex_content (str): The LaTeX document content.
            
        Returns:
            bytes: PDF document as bytes.
            
        Raises:
            Exception: If PDF generation fails.
        """
        return self.pdf_generator.generate_pdf_from_latex(latex_content)
    
    # Template definitions
    def _get_modern_cv_template(self):
        """Get the modern CV template content."""
        return r"""% Modern CV Template
\documentclass[11pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage{geometry}
\usepackage{hyperref}
\usepackage{enumitem}
\usepackage{fontawesome}
\usepackage{titlesec}
\usepackage{xcolor}

% Custom colors
\definecolor{primary}{RGB}{70, 130, 180}
\definecolor{lightgray}{RGB}{240, 240, 240}

% Setup page margins
\geometry{left=1.5cm, right=1.5cm, top=1.5cm, bottom=1.5cm}

% Style settings
\titleformat{\section}{\Large\bfseries\color{primary}}{\thesection}{1em}{}[\titlerule]
\setlength{\parindent}{0pt}
\titlespacing{\section}{0pt}{12pt}{6pt}

% Hyperlink setup
\hypersetup{
    colorlinks=true,
    linkcolor=primary,
    filecolor=primary,
    urlcolor=primary,
}

\begin{document}

% Header
{\centering
    {\Huge\textbf{{\color{primary}{{name}}}}}\par
    \vspace{0.5em}
    {\large
    \faEnvelope\,\href{mailto:{{email}}}{{{email}}} \hfill
    \faPhone\,{{phone}}
    
    \ifx\{{website}}\empty\else
    \faLink\,{{website}} \hfill
    \fi
    \ifx\{{linkedin}}\empty\else
    \faLinkedin\,{{linkedin}}
    \fi
    }
    \par
}
\vspace{1em}

% Summary
\section{Summary}
{{summary}}

% Experience
\section{Experience}
{{experience}}

% Education
\section{Education}
{{education}}

% Skills
\section{Skills}
{{skills}}

% Projects
\section{Projects}
{{projects}}

% Publications
\section{Publications}
{{publications}}

% Languages
\ifx\{{languages}}\empty\else
\section{Languages}
{{languages}}
\fi

% Certifications
\ifx\{{certifications}}\empty\else
\section{Certifications}
{{certifications}}
\fi

\end{document}
"""

    def _get_academic_cv_template(self):
        """Get the academic CV template content."""
        return r"""% Academic CV Template
\documentclass[11pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage{geometry}
\usepackage{hyperref}
\usepackage{enumitem}
\usepackage{fontawesome}
\usepackage{titlesec}
\usepackage{xcolor}
\usepackage{biblatex}

% Custom colors
\definecolor{primary}{RGB}{50, 50, 100}
\definecolor{lightgray}{RGB}{240, 240, 240}

% Setup page margins
\geometry{left=2cm, right=2cm, top=2cm, bottom=2cm}

% Style settings
\titleformat{\section}{\Large\bfseries\color{primary}}{\thesection}{1em}{}[\titlerule]
\setlength{\parindent}{0pt}
\titlespacing{\section}{0pt}{12pt}{6pt}

% Hyperlink setup
\hypersetup{
    colorlinks=true,
    linkcolor=primary,
    filecolor=primary,
    urlcolor=primary,
}

\begin{document}

% Header
{\centering
    {\Huge\textbf{{\color{primary}{{name}}}}}\par
    \vspace{0.5em}
    {\large
    \faEnvelope\,\href{mailto:{{email}}}{{{email}}} \hfill
    \faPhone\,{{phone}}
    
    \ifx\{{website}}\empty\else
    \faLink\,{{website}} \hfill
    \fi
    \ifx\{{linkedin}}\empty\else
    \faLinkedin\,{{linkedin}}
    \fi
    }
    \par
}
\vspace{1em}

% Research Interests
\section{Research Interests}
{{summary}}

% Education
\section{Education}
{{education}}

% Publications
\section{Publications}
{{publications}}

% Experience
\section{Experience}
{{experience}}

% Research Projects
\section{Research Projects}
{{projects}}

% Teaching Experience
\ifx\{{teaching}}\empty\else
\section{Teaching Experience}
{{teaching}}
\fi

% Skills
\section{Skills}
{{skills}}

% Languages
\ifx\{{languages}}\empty\else
\section{Languages}
{{languages}}
\fi

% Grants & Awards
\ifx\{{awards}}\empty\else
\section{Grants & Awards}
{{awards}}
\fi

% References
\ifx\{{references}}\empty\else
\section{References}
{{references}}
\fi

\end{document}
"""

    def _get_classic_cv_template(self):
        """Get the classic CV template content."""
        return r"""% Classic CV Template
\documentclass[11pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage{geometry}
\usepackage{hyperref}
\usepackage{enumitem}
\usepackage{fontawesome}
\usepackage{titlesec}

% Setup page margins
\geometry{left=2cm, right=2cm, top=2cm, bottom=2cm}

% Style settings
\titleformat{\section}{\Large\bfseries}{\thesection}{1em}{}[\titlerule]
\setlength{\parindent}{0pt}
\titlespacing{\section}{0pt}{12pt}{6pt}

% Hyperlink setup
\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=black,
    urlcolor=blue,
}

\begin{document}

% Header
{\centering
    {\Huge\textbf{{{name}}}}\par
    \vspace{0.5em}
    {\large
    \faEnvelope\,\href{mailto:{{email}}}{{{email}}} \hfill
    \faPhone\,{{phone}}
    
    \ifx\{{website}}\empty\else
    \faLink\,{{website}} \hfill
    \fi
    \ifx\{{linkedin}}\empty\else
    \faLinkedin\,{{linkedin}}
    \fi
    }
    \par
}
\vspace{1em}

% Summary
\section{Summary}
{{summary}}

% Experience
\section{Experience}
{{experience}}

% Education
\section{Education}
{{education}}

% Skills
\section{Skills}
{{skills}}

% Projects
\section{Projects}
{{projects}}

% Publications
\section{Publications}
{{publications}}

% Languages
\ifx\{{languages}}\empty\else
\section{Languages}
{{languages}}
\fi

% Certifications
\ifx\{{certifications}}\empty\else
\section{Certifications}
{{certifications}}
\fi

\end{document}
"""
