"""
Minimal CV generator that uses the most basic LaTeX possible.
This is a last-resort fallback when all other LaTeX generation methods fail.
"""

import os
import subprocess
import tempfile

class MinimalCVGenerator:
    """Ultra-minimal CV generator for when all else fails."""
    
    def generate_minimal_cv(self, cv_data):
        """Generate an extremely simple LaTeX CV.
        
        Args:
            cv_data (dict): Basic CV data
            
        Returns:
            str: Ultra-minimal LaTeX document
        """
        name = self._safe_text(cv_data.get('name', 'Name'))
        email = self._safe_text(cv_data.get('email', 'Email'))
        phone = self._safe_text(cv_data.get('phone', 'Phone'))
        summary = self._safe_text(cv_data.get('summary', ''))
        
        # Create the most basic LaTeX document possible
        return f"""\\documentclass{{article}}
\\usepackage{{geometry}}
\\geometry{{margin=1in}}
\\begin{{document}}

\\begin{{center}}
\\textbf{{{name}}}\\\\
{email} | {phone}
\\end{{center}}

\\section*{{Summary}}
{summary}

\\section*{{Education}}
{self._format_simple_items(cv_data.get('education', []))}

\\section*{{Experience}}
{self._format_simple_items(cv_data.get('experience', []))}

\\section*{{Skills}}
{self._format_simple_items(cv_data.get('skills', []))}

\\end{{document}}
"""
    
    def _safe_text(self, text):
        """Create LaTeX-safe text.
        
        Args:
            text: Input text
            
        Returns:
            str: Safe text for LaTeX
        """
        if not text:
            return ""
            
        # Convert to string and escape basic LaTeX characters
        text = str(text)
        replacements = {
            '&': '\\&',
            '%': '\\%',
            '$': '\\$',
            '#': '\\#',
            '_': '\\_',
            '{': '\\{',
            '}': '\\}',
            '\\': '\\textbackslash ',
            '~': '\\textasciitilde ',
            '^': '\\textasciicircum '
        }
        
        for char, replacement in replacements.items():
            text = text.replace(char, replacement)
            
        return text
    
    def _format_simple_items(self, items):
        """Format items as a simple list without fancy environments.
        
        Args:
            items: List or string to format
            
        Returns:
            str: Simple formatted text
        """
        if not items:
            return ""
            
        result = ""
        
        # Handle different data types simply
        if isinstance(items, str):
            return self._safe_text(items)
            
        if isinstance(items, list):
            for item in items:
                if isinstance(item, dict) and 'description' in item:
                    # Just get the description text
                    desc = item['description']
                    if isinstance(desc, str):
                        result += f"- {self._safe_text(desc)}\\\\\\noindent\n"
                    elif isinstance(desc, list):
                        for point in desc:
                            result += f"- {self._safe_text(point)}\\\\\\noindent\n"
                elif isinstance(item, str):
                    result += f"- {self._safe_text(item)}\\\\\\noindent\n" 
                    
        return result
        
    def generate_pdf(self, cv_data):
        """Generate a PDF using ultra-minimal LaTeX.
        
        Args:
            cv_data (dict): The CV data
            
        Returns:
            bytes: PDF content
        """
        # Generate minimal LaTeX
        latex_content = self.generate_minimal_cv(cv_data)
        
        # Write to temp file and compile
        with tempfile.TemporaryDirectory() as temp_dir:
            tex_path = os.path.join(temp_dir, 'minimal_cv.tex')
            with open(tex_path, 'w') as tex_file:
                tex_file.write(latex_content)
                
            # Write the LaTeX content to a debug file for inspection
            debug_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'debug_minimal.tex')
            with open(debug_path, 'w') as debug_file:
                debug_file.write(latex_content)
                
            # Run pdflatex with minimal options
            subprocess.run(
                ['pdflatex', '-interaction=nonstopmode', tex_path],
                cwd=temp_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Try a second run in case of references
            subprocess.run(
                ['pdflatex', '-interaction=nonstopmode', tex_path],
                cwd=temp_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Check if PDF was created
            pdf_path = os.path.join(temp_dir, 'minimal_cv.pdf')
            if os.path.exists(pdf_path):
                with open(pdf_path, 'rb') as pdf_file:
                    return pdf_file.read()
            else:
                # Create a text file as last resort
                text_content = f"""CV FOR: {cv_data.get('name', 'Name')}
CONTACT: {cv_data.get('email', 'Email')} | {cv_data.get('phone', 'Phone')}

SUMMARY:
{cv_data.get('summary', '')}

Note: PDF generation failed. This is a text fallback.
"""
                text_path = os.path.join(temp_dir, 'cv_fallback.txt')
                with open(text_path, 'w') as text_file:
                    text_file.write(text_content)
                    
                # Read the generated text file
                with open(text_path, 'rb') as file:
                    return file.read()
