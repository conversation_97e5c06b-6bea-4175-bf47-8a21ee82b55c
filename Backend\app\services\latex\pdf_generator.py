"""
PDF Generator Module
Handles conversion of LaTeX documents to PDF using pdflatex.
"""
import os
import subprocess
import tempfile

class PDFGenerator:
    """Generates PDF documents from LaTeX content."""
    
    def __init__(self):
        """Initialize the PDF generator."""
        pass
    
    def check_latex_installation(self):
        """Check if LaTeX is properly installed.
        
        Returns:
            bool: True if LaTeX is installed and working, False otherwise.
        """
        try:
            result = subprocess.run(['pdflatex', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (FileNotFoundError, subprocess.TimeoutExpired):
            return False
    
    def generate_pdf_from_latex(self, latex_content):
        """Generate a PDF from LaTeX content.

        Args:
            latex_content (str): The LaTeX document content

        Returns:
            bytes: The generated PDF content
        """
        with tempfile.TemporaryDirectory() as temp_dir:
            tex_filename = 'cv.tex'
            tex_path = os.path.join(temp_dir, tex_filename)

            # Write the LaTeX content to the .tex file
            with open(tex_path, 'w', encoding='utf-8') as tex_file:
                tex_file.write(latex_content)

            try:
                # Run pdflatex with only the filename, from inside the working dir
                result = subprocess.run(
                    ['pdflatex', '-interaction=nonstopmode', tex_filename],
                    cwd=temp_dir,
                    capture_output=True,
                    text=True,
                    timeout=30  # You can increase this if compilation needs more time
                )

                if result.returncode != 0:
                    print("LaTeX compilation failed:")
                    print(result.stderr)
                    print(result.stdout)
                    raise Exception("PDF generation failed, LaTeX error")

                # Read the generated PDF
                pdf_path = os.path.join(temp_dir, 'cv.pdf')
                with open(pdf_path, 'rb') as pdf_file:
                    return pdf_file.read()

            except subprocess.TimeoutExpired:
                print("LaTeX compilation timed out")
                raise Exception("PDF generation timed out")
            except Exception as e:
                print(f"Error generating PDF: {e}")
                raise Exception(f"PDF generation failed: {str(e)}")
