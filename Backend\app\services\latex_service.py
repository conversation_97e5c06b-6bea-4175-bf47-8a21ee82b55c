"""
LaTeX CV generation service - Modular Version
This module provides functionality to generate LaTeX CVs based on user input and AI suggestions.

This is now a lightweight wrapper around the new modular LaTeX service architecture located in 
the 'latex/' directory. The code has been refactored into specialized components for better 
maintainability and clearer software design.

- latex_renderer.py - Handles rendering of LaTeX templates and proper escaping
- latex_formatter.py - Formats CV data into LaTeX-friendly structures
- pdf_generator.py - Converts LaTeX documents to PDF using pdflatex
- latex_service.py - Main service that integrates the components
"""
from .latex import LaTeXService

# Create an instance for backwards compatibility with existing imports
latex_service = LaTeXService()

# Export both the class and the instance for backwards compatibility
__all__ = ['LaTeXService', 'latex_service']
