import os
import json
import faiss
import numpy as np
from dotenv import load_dotenv
from sentence_transformers import SentenceTransformer
import openai
from datetime import date

load_dotenv()

# Config OpenRouter
openai.api_key = os.getenv("OPENROUTER_API_KEY")
openai.api_base = "https://openrouter.ai/api/v1"

# Modèle gratuit proposé (tu peux changer si tu veux)
MODEL_NAME = "mistralai/mistral-7b-instruct"

# Chargement modèle d'embedding
model = SentenceTransformer("all-MiniLM-L6-v2")

# Charger corpus
docs_path = os.path.join(os.path.dirname(__file__), "document_store.txt")
with open(docs_path, encoding="utf-8") as f:
    docs = f.read().split("\n\n")

doc_embeddings = model.encode(docs, convert_to_numpy=True)
embedding_dim = doc_embeddings.shape[1]

index = faiss.IndexFlatL2(embedding_dim)
index.add(doc_embeddings)

def get_context(text: str, k: int = 3) -> str:
    query_vec = model.encode([text], convert_to_numpy=True)
    _, top_indices = index.search(query_vec, k)
    return "\n\n".join([docs[i] for i in top_indices[0]])

def build_prompt(context: str, job_text: str) -> str:
    # Format date comme 5/10/2025
    today = date.today().strftime("%#d/%#m/%Y")
    # Remplace par "%#d/%#m/%Y" si tu es sous Windows

    return f"""Tu es un assistant intelligent chargé d'extraire les informations essentielles d'une offre d'emploi rédigée en texte libre. Ta mission est de produire un objet JSON structuré contenant les éléments suivants.

Voici des informations de contexte utiles :
{context}

Offre d'emploi à analyser :
<<<
{job_text}
>>>

Instructions :
- Résume précisément le contenu de l'offre dans la clé `description`, en 2 à 4 phrases claires et concises en français.
- Renseigne la clé `appliedDate` avec la date d'aujourd'hui : "{today}".
- Fixe le champ `status` à la valeur `"applied"`.
- Complète les autres champs (titre du poste, entreprise, lieu, etc.) si l'information est disponible dans le texte.
- Pour le champ `notes`, extrait les compétences requises, l'expérience demandée, et autres exigences importantes mentionnées dans l'offre (technologies, diplômes, langues, etc.). Formate cette information de manière claire et concise.
- Garde les champs `id`, `remindDate` et `url` à `null`, sauf indication contraire.
- Respecte strictement la structure JSON suivante, sans ajouter d'autres champs.

Format attendu :

{{
  "appliedDate": "{today}",
  "company": null,
  "description": "Résumé clair et concis de l'offre",
  "id": null,
  "location": null,
  "notes": "Compétences requises: [liste des technologies/compétences]; Expérience: [années d'expérience]; Autres exigences: [diplômes, langues, etc.]",
  "remindDate": null,
  "status": "applied",
  "title": null,
  "url": null
}}"""


def parse_offer(job_text: str) -> dict:
    context = get_context(job_text)
    prompt = build_prompt(context, job_text)

    response = openai.ChatCompletion.create(
        model=MODEL_NAME,
        messages=[
            {"role": "system", "content": "Tu es un assistant utile."},
            {"role": "user", "content": prompt}
        ],
        temperature=0.3,
        max_tokens=1000
    )

    result = response.choices[0].message["content"]
    try:
        json_start = result.find("{")
        json_str = result[json_start:]
        return json.loads(json_str)
    except Exception as e:
        return {"error": "Erreur parsing JSON", "details": str(e), "raw_response": result}
