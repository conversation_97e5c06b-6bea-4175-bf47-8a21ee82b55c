from .firebase_db import db
from firebase_admin import auth

def get_user_profile(user_id: str):
    """Fetches a user's profile. If not found, creates a default one from Auth data."""
    try:
        user_ref = db.collection('users').document(user_id)
        doc = user_ref.get()
        if doc.exists:
            return doc.to_dict()
        else:
            # Fallback for users that might exist in Auth but not Firestore
            auth_user = auth.get_user(user_id)
            default_profile = {
                'email': auth_user.email or '',
                'fullName': auth_user.display_name or '',
                'headline': '',
            }
            user_ref.set(default_profile)
            return default_profile
    except Exception as e:
        raise Exception(f"Error getting user profile: {str(e)}")

def update_user_profile(user_id: str, profile_data: dict):
    """Updates a user's profile data in Firestore and Firebase Auth."""
    try:
        allowed_fields = ['fullName', 'headline']
        data_to_update = {k: v for k, v in profile_data.items() if k in allowed_fields}

        if not data_to_update:
            raise ValueError("No valid fields provided for update.")

        # Update Firestore
        user_ref = db.collection('users').document(user_id)
        user_ref.update(data_to_update)

        # Also update Firebase Auth displayName if fullName is changed
        if 'fullName' in data_to_update:
            auth.update_user(user_id, display_name=data_to_update['fullName'])

        return {"message": "Profile updated successfully"}
    except Exception as e:
        raise Exception(f"Error updating user profile: {str(e)}")
