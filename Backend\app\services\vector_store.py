import faiss
import numpy as np
import uuid
from sentence_transformers import SentenceTransformer

model = SentenceTransformer("all-MiniLM-L6-v2")

# Initialisation FAISS
dimension = 384  # dimension du modèle all-MiniLM
index = faiss.IndexFlatL2(dimension)

# Mapping FAISS ID -> contenu
document_mapping = {}

def embed_text(text):
    return model.encode([text])[0]

def add_document(text, metadata=None):
    vector = embed_text(text)
    doc_id = str(uuid.uuid4())
    index.add(np.array([vector]))
    document_mapping[len(document_mapping)] = {
        "id": doc_id,
        "text": text,
        "metadata": metadata or {}
    }
    return doc_id

def search_similar_documents(query, k=3):
    query_vector = embed_text(query)
    distances, indices = index.search(np.array([query_vector]), k)
    results = []
    for idx in indices[0]:
        if idx in document_mapping:
            results.append(document_mapping[idx])
    return results
