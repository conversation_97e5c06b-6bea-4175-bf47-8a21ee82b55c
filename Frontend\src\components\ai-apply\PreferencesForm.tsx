
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export const PreferencesForm = ({ onActivate }: { onActivate: (preferences: any) => void }) => {
  const [preferences, setPreferences] = useState({
    jobTitles: "",
    locations: "",
    keywords: "",
    excludeKeywords: "",
    experience: "1-3 years",
    remoteOnly: false,
    coverLetter: true,
    maxApplications: "5",
  });
  const [isActivating, setIsActivating] = useState(false);
  const { toast } = useToast();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setPreferences((prev) => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setPreferences((prev) => ({ ...prev, [name]: checked }));
  };

  const handleActivate = () => {
    setIsActivating(true);
    
    // Simulate API call
    setTimeout(() => {
      onActivate(preferences);
      
      toast({
        title: "AI Agent Activated",
        description: "Your AI apply agent has been activated and will start looking for jobs matching your preferences.",
      });
      
      setIsActivating(false);
    }, 1500);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>AI Apply Agent Preferences</CardTitle>
        <CardDescription>
          Configure your AI agent to automatically find and apply to jobs that match your preferences
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="jobTitles">Job Titles</Label>
          <Textarea
            id="jobTitles"
            name="jobTitles"
            value={preferences.jobTitles}
            onChange={handleChange}
            placeholder="Frontend Developer, React Developer, UI Engineer (one per line)"
            className="min-h-[100px]"
          />
          <p className="text-xs text-muted-foreground">Enter job titles that you're interested in, one per line</p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="locations">Locations</Label>
          <Textarea
            id="locations"
            name="locations"
            value={preferences.locations}
            onChange={handleChange}
            placeholder="New York, San Francisco, Remote (one per line)"
            className="min-h-[80px]"
          />
          <p className="text-xs text-muted-foreground">Enter locations where you want to work, one per line</p>
        </div>
        
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="advanced">
            <AccordionTrigger>Advanced Preferences</AccordionTrigger>
            <AccordionContent className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="keywords">Keywords to Include</Label>
                <Textarea
                  id="keywords"
                  name="keywords"
                  value={preferences.keywords}
                  onChange={handleChange}
                  placeholder="React, TypeScript, Next.js (comma separated)"
                />
                <p className="text-xs text-muted-foreground">Skills or keywords that should be in the job description</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="excludeKeywords">Keywords to Exclude</Label>
                <Textarea
                  id="excludeKeywords"
                  name="excludeKeywords"
                  value={preferences.excludeKeywords}
                  onChange={handleChange}
                  placeholder="PHP, WordPress, Java (comma separated)"
                />
                <p className="text-xs text-muted-foreground">Skills or keywords that you want to avoid</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="experience">Experience Level</Label>
                  <select
                    id="experience"
                    name="experience"
                    value={preferences.experience}
                    onChange={handleChange}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="Entry level">Entry level</option>
                    <option value="1-3 years">1-3 years</option>
                    <option value="3-5 years">3-5 years</option>
                    <option value="5+ years">5+ years</option>
                    <option value="7+ years">7+ years</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxApplications">Max Applications per Day</Label>
                  <Input
                    id="maxApplications"
                    name="maxApplications"
                    type="number"
                    min="1"
                    max="20"
                    value={preferences.maxApplications}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="remoteOnly">Remote Only</Label>
                  <p className="text-xs text-muted-foreground">Only show remote job opportunities</p>
                </div>
                <Switch
                  id="remoteOnly"
                  checked={preferences.remoteOnly}
                  onCheckedChange={(checked) => handleSwitchChange("remoteOnly", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="coverLetter">Generate Cover Letters</Label>
                  <p className="text-xs text-muted-foreground">Automatically generate custom cover letters for each application</p>
                </div>
                <Switch
                  id="coverLetter"
                  checked={preferences.coverLetter}
                  onCheckedChange={(checked) => handleSwitchChange("coverLetter", checked)}
                />
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
      <CardFooter>
        <Button onClick={handleActivate} disabled={isActivating || !preferences.jobTitles || !preferences.locations}>
          {isActivating ? "Activating..." : "Activate AI Apply Agent"}
        </Button>
      </CardFooter>
    </Card>
  );
};
