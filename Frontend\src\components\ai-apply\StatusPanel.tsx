import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress"; // Keep for potential future use
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Loader2, ListChecks, SearchX } from "lucide-react";

interface StatusPanelProps {
  active: boolean;
  onDeactivate: () => void;
  preferences: any; // Consider defining a more specific type for preferences
  searchResults: any[]; // Consider defining a type for a Job result item
  isSearching: boolean;
  searchError: string | null;
}

export const StatusPanel = ({
  active,
  onDeactivate,
  preferences,
  searchResults,
  isSearching,
  searchError,
}: StatusPanelProps) => {
  const [isDeactivating, setIsDeactivating] = useState(false);

  // Removed useEffect for simulated data

  const handleDeactivate = () => {
    setIsDeactivating(true);
    // Simulate API call for deactivation if needed, or just call onDeactivate directly
    setTimeout(() => {
      onDeactivate();
      setIsDeactivating(false);
    }, 500); // Reduced delay
  };

  if (!active) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>AI Agent Status</CardTitle>
            <CardDescription>
              {isSearching ? "Searching for jobs..." : searchError ? "Error encountered" : `Monitoring for jobs based on your preferences.`}
            </CardDescription>
          </div>
          <Badge variant={isSearching ? "secondary" : searchError ? "destructive" : "default"}>
            {isSearching ? "Searching" : searchError ? "Error" : "Active"}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {preferences && (
          <div className="bg-muted/50 rounded-lg p-4 border">
            <h4 className="font-medium mb-2 text-sm">Active Preferences:</h4>
            <ul className="list-disc list-inside text-xs space-y-1">
              {preferences.jobTitles && <li>Job Titles: {preferences.jobTitles.split('\n').join(', ')}</li>}
              {preferences.locations && <li>Locations: {preferences.locations.split('\n').join(', ')}</li>}
              {preferences.keywords && <li>Keywords: {preferences.keywords}</li>}
              {preferences.excludeKeywords && <li>Exclude: {preferences.excludeKeywords}</li>}
              {preferences.experience && <li>Experience: {preferences.experience}</li>}
              <li>Remote Only: {preferences.remoteOnly ? "Yes" : "No"}</li>
            </ul>
          </div>
        )}

        {isSearching && (
          <div className="flex flex-col items-center justify-center py-10">
            <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">Searching for jobs, please wait...</p>
          </div>
        )}

        {searchError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Search Failed</AlertTitle>
            <AlertDescription>{searchError}</AlertDescription>
          </Alert>
        )}

        {!isSearching && !searchError && searchResults.length > 0 && (
          <div className="bg-muted/50 rounded-lg p-4 border">
            <h4 className="font-medium mb-2 flex items-center">
              <ListChecks className="h-5 w-5 mr-2 text-green-600" /> Found Jobs ({searchResults.length})
            </h4>
            <ul className="space-y-3">
              {searchResults.map((job, index) => (
                <li key={job.id || index} className="p-3 border rounded-md bg-background shadow-sm">
                  <h5 className="font-semibold text-sm">{job.title || "N/A"}</h5>
                  <p className="text-xs text-muted-foreground">{job.company || "N/A"} - {job.location || "N/A"}</p>
                  {job.url && <a href={job.url} target="_blank" rel="noopener noreferrer" className="text-xs text-blue-500 hover:underline">View Job</a>}
                  {/* Add more job details as needed */}
                </li>
              ))}
            </ul>
          </div>
        )}

        {!isSearching && !searchError && searchResults.length === 0 && (
           <div className="flex flex-col items-center justify-center py-10 text-center">
            <SearchX className="h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No jobs found matching your current preferences.</p>
            <p className="text-xs text-muted-foreground mt-1">Try adjusting your search criteria in the preferences form.</p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button 
          variant="outline"
          className="w-full"
          onClick={handleDeactivate}
          disabled={isDeactivating}
        >
          {isDeactivating ? "Deactivating..." : "Deactivate AI Agent & Clear Preferences"}
        </Button>
      </CardFooter>
    </Card>
  );
};
