import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { extractPdfText } from "@/utils/extractPDFTextAI";
import { extractCVDataFromText } from "@/utils/extractCVData";
import { getCVFeedbackAndScore } from "@/utils/cvFeedbackAI";
import { generateCoverLetterAI } from "@/utils/coverLetterAI";
import { getTemplates, generateCV, generateLatexCV } from "@/services/apiCV";
import { PDFViewer } from "@/components/pdf-viewer/PDFViewer";

export const CVForm = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [templates, setTemplates] = useState<string[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState("modern");
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [enhanceWithAI, setEnhanceWithAI] = useState(true);
  const [cvData, setCvData] = useState({
    name: "",
    email: "",
    phone: "",
    website: "",
    linkedin: "",
    summary: "",
    experience: "",
    education: "",
    skills: "",
  });
  const [jobDescription, setJobDescription] = useState("");
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [coverLetter, setCoverLetter] = useState("");
  const [cvScore, setCvScore] = useState<number | null>(null);
  const [cvFeedback, setCvFeedback] = useState("");
  const [pdfExtracted, setPdfExtracted] = useState(false);
  const { toast } = useToast();

  // Fetch available templates when component mounts
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const availableTemplates = await getTemplates();
        setTemplates(availableTemplates);
        if (availableTemplates.length > 0) {
          setSelectedTemplate(availableTemplates[0]);
        }
      } catch (error) {
        console.error("Failed to fetch templates:", error);
        toast({
          title: "Error",
          description: "Failed to fetch CV templates. Using default template.",
        });
      }
    };
    
    fetchTemplates();
  }, []);
  
  // Clean up PDF URL when component unmounts
  useEffect(() => {
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [pdfUrl]);
  
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setCvData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setUploadedFile(e.target.files[0]);
      toast({
        title: "CV Uploaded",
        description: `File "${e.target.files[0].name}" has been uploaded.`,
      });
      setPdfExtracted(false);
      // Extract text from PDF using AI OCR
      const file = e.target.files[0];
      const text = await extractPdfText(file);
      console.log("PDF Extraction Successful");
      setPdfExtracted(true);
      toast({
        title: "PDF Extracted",
        description: "The PDF was successfully extracted to text.",
      });
      try {
        const aiData = await extractCVDataFromText(text);
        setCvData(aiData);
      } catch (err) {
        toast({
          title: "AI Extraction Failed",
          description: "Could not extract CV data automatically.",
        });
      }
    }
  };

  const handleGenerateCV = async () => {
    if (!pdfExtracted && uploadedFile) {
      toast({
        title: "PDF Not Yet Extracted",
        description:
          "Please wait until the PDF is extracted before getting AI feedback.",
      });
      return;
    }
    setIsGenerating(true);
    setCvScore(null);
    setCvFeedback("");
    if (jobDescription && (uploadedFile || cvData.name)) {
      try {
        const aiFeedback = await getCVFeedbackAndScore(cvData, jobDescription);
        setCvScore(aiFeedback.score);
        setCvFeedback(aiFeedback.feedback);
      } catch (err) {
        setCvFeedback("Could not generate feedback.");
      }
    }
    setIsGenerating(false);
    toast({
      title: "AI Feedback Ready",
      description: "See your CV score and improvement suggestions below.",
    });
  };

  const handleGenerateLetter = async () => {
    if (!pdfExtracted && uploadedFile) {
      toast({
        title: "PDF Not Yet Extracted",
        description:
          "Please wait until the PDF is extracted before generating a cover letter.",
      });
      return;
    }
    setIsGenerating(true);
    let letter = "";
    if (jobDescription && (uploadedFile || cvData.name)) {
      try {
        letter = await generateCoverLetterAI(cvData, jobDescription);
        setCoverLetter(letter);
      } catch (err) {
        letter = "Could not generate cover letter.";
        setCoverLetter(letter);
      }
    }
    setIsGenerating(false);
    toast({
      title: "Cover Letter Generated",
      description: "",
    });
  };


  
  const handleGeneratePDF = async () => {
    if (!pdfExtracted && uploadedFile) {
      toast({
        title: "PDF Not Yet Extracted",
        description: "Please wait until the PDF is extracted before generating PDF.",
      });
      return;
    }
    
    setIsGeneratingPDF(true);
    setPdfBlob(null);
    setPdfUrl(null);
    
    try {
      // Format CV data as proper object structure for the API
      const formattedCvData = {
        name: cvData.name,
        email: cvData.email,
        phone: cvData.phone,
        website: cvData.website,
        linkedin: cvData.linkedin,
        summary: cvData.summary,
        experience: cvData.experience.split("\n\n").map(exp => ({
          description: exp
        })),
        education: cvData.education.split("\n\n").map(edu => ({
          description: edu
        })),
        skills: cvData.skills.split(",").map(skill => skill.trim()),
      };
      
      const pdfBlob = await generateCV(
        formattedCvData,
        jobDescription,
        selectedTemplate,
        enhanceWithAI
      );
      
      setPdfBlob(pdfBlob);
      const url = URL.createObjectURL(pdfBlob);
      setPdfUrl(url);
      
      toast({
        title: "CV Generated Successfully",
        description: "Your professional CV has been created and is ready to download.",
      });
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast({
        title: "Error",
        description: "Failed to generate PDF. Please try again.",
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };
  
  return (
    <Tabs defaultValue="form" className="w-full">
      <TabsList className="grid w-full max-w-md mx-auto grid-cols-2">
        <TabsTrigger value="form">Fill Form</TabsTrigger>
        <TabsTrigger value="upload">Upload CV</TabsTrigger>
      </TabsList>
      <div className="mt-6">
        <TabsContent value="form">
          <Card>
            <CardHeader>
              <CardTitle>Create Your CV</CardTitle>
              <CardDescription>
                Fill in your details to generate a personalized CV
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={cvData.name}
                  onChange={handleChange}
                  placeholder="John Doe"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={cvData.email}
                    onChange={handleChange}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={cvData.phone}
                    onChange={handleChange}
                    placeholder="+****************"
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    name="website"
                    type="url"
                    value={cvData.website}
                    onChange={handleChange}
                    placeholder="https://yourwebsite.com"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="linkedin">LinkedIn</Label>
                  <Input
                    id="linkedin"
                    name="linkedin"
                    value={cvData.linkedin}
                    onChange={handleChange}
                    placeholder="https://linkedin.com/in/username"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="summary">Professional Summary</Label>
                <Textarea
                  id="summary"
                  name="summary"
                  value={cvData.summary}
                  onChange={handleChange}
                  placeholder="A brief summary of your professional background and career goals..."
                  className="min-h-[100px]"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="experience">Work Experience</Label>
                <Textarea
                  id="experience"
                  name="experience"
                  value={cvData.experience}
                  onChange={handleChange}
                  placeholder="List your work experience, including job titles, companies, dates, and key responsibilities..."
                  className="min-h-[150px]"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="education">Education</Label>
                <Textarea
                  id="education"
                  name="education"
                  value={cvData.education}
                  onChange={handleChange}
                  placeholder="List your educational background, including degrees, institutions, and graduation dates..."
                  className="min-h-[100px]"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="skills">Skills</Label>
                <Textarea
                  id="skills"
                  name="skills"
                  value={cvData.skills}
                  onChange={handleChange}
                  placeholder="List your relevant skills, separated by commas..."
                  className="min-h-[100px]"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upload">
          <Card>
            <CardHeader>
              <CardTitle>Upload Existing CV</CardTitle>
              <CardDescription>
                Upload your existing CV to generate a personalized version
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid w-full max-w-sm items-center gap-1.5">
                <Label htmlFor="cv-upload">Upload CV</Label>
                <Input
                  id="cv-upload"
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={handleFileChange}
                />
                <p className="text-xs text-muted-foreground">
                  Accepted formats: PDF, DOC, DOCX
                </p>
              </div>
              {uploadedFile && (
                <div className="bg-muted p-3 rounded-md">
                  <p className="text-sm">
                    Uploaded:{" "}
                    <span className="font-medium">{uploadedFile.name}</span>
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </div>

      <div className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Job Description</CardTitle>
            <CardDescription>
              Paste the job description to tailor your CV and cover letter
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Paste the job description here..."
              value={jobDescription}
              onChange={(e) => setJobDescription(e.target.value)}
              className="min-h-[200px]"
            />
          </CardContent>
          <CardFooter className="flex justify-between flex-col sm:flex-row gap-4">
            <Button
              onClick={handleGenerateCV}
              disabled={isGenerating || (!uploadedFile && !cvData.name)}
              className="w-full sm:w-auto"
            >
              {isGenerating ? "Getting AI Feedback..." : "Get AI Feedback"}
            </Button>
            <Button
              onClick={handleGenerateLetter}
              disabled={
                isGenerating ||
                (!uploadedFile && !cvData.name) ||
                !jobDescription
              }
              variant="outline"
              className="w-full sm:w-auto"
            >
              {isGenerating ? "Generating Letter..." : "Generate Cover Letter"}
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Template selection and generate buttons */}
      <div className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Generate Your Professional CV</CardTitle>
            <CardDescription>
              Choose your template style and generation options
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="space-y-4">
                <Label htmlFor="template" className="text-lg font-semibold">Choose Template Style</Label>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                  {templates.length > 0 ? (
                    templates.map((template) => {
                      // Extract template name without _cv suffix if present
                      const displayName = template.replace('_cv', '');
                      const capitalizedName = displayName.charAt(0).toUpperCase() + displayName.slice(1);
                      
                      return (
                        <div
                          key={template}
                          className={`border-2 rounded-lg p-3 cursor-pointer transition-all hover:shadow-lg ${selectedTemplate === template ? 'border-primary ring-2 ring-primary/30 bg-primary/5' : 'hover:border-gray-400'}`}
                          onClick={() => setSelectedTemplate(template)}
                        >
                          <div className="text-center">
                            <div className="relative mb-3 rounded overflow-hidden shadow-sm">
                              <img 
                                src={`/images/templates/${displayName}.svg`} 
                                alt={`${capitalizedName} template preview`}
                                className="w-full h-48 object-cover"
                                onError={(e) => {
                                  // Fallback if image doesn't exist
                                  e.currentTarget.src = '/images/templates/modern.svg';
                                }}
                              />
                              {selectedTemplate === template && (
                                <div className="absolute top-0 right-0 bg-primary text-white p-1 px-2 rounded-bl-lg">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center justify-center space-x-2">
                              <span className="font-medium text-sm">{capitalizedName}</span>
                              {selectedTemplate === template && (
                                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Selected</span>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="border-2 rounded-lg p-3 cursor-pointer border-primary ring-2 ring-primary/30 bg-primary/5">
                      <div className="text-center">
                        <div className="mb-3 rounded overflow-hidden shadow-sm">
                          <img 
                            src="/images/templates/modern.svg" 
                            alt="Modern template preview"
                            className="w-full h-48 object-cover"
                          />
                        </div>
                        <div className="flex items-center justify-center space-x-2">
                          <span className="font-medium text-sm">Modern</span>
                          <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Selected</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="p-4 border rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50">
                <h3 className="text-md font-semibold mb-3 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                  </svg>
                  AI Enhancement Options
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="enhance-ai"
                        checked={enhanceWithAI}
                        onChange={(e) => setEnhanceWithAI(e.target.checked)}
                        className="h-5 w-5 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <label htmlFor="enhance-ai" className="ml-2 block text-sm font-medium text-gray-700">
                        Enhance CV with AI
                      </label>
                    </div>
                  </div>
                  
                  {enhanceWithAI && (
                    <div className="pl-8 text-sm text-gray-600 space-y-2">
                      <p className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Tailors your CV content to match job requirements
                      </p>
                      <p className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Improves wording and formatting
                      </p>
                      <p className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Highlights relevant skills and accomplishments
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-center pt-6 border-t">
            <div className="w-full max-w-md bg-gradient-to-r from-slate-50 to-slate-100 p-5 rounded-lg shadow-sm border">
              <div className="text-center mb-4">
                <h3 className="font-semibold text-gray-900">Ready to create your professional CV?</h3>
                <p className="text-sm text-gray-600 mt-1">Click below to generate your CV with the selected template</p>
              </div>
              
              <Button
                onClick={handleGeneratePDF}
                disabled={isGeneratingPDF || (!uploadedFile && !cvData.name) || !jobDescription}
                className={`w-full relative py-6 text-lg font-medium transition-all ${isGeneratingPDF ? 'bg-primary/80' : 'bg-primary hover:bg-primary/90 hover:shadow-md'}`}
                size="lg"
              >
                {isGeneratingPDF ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating your professional CV...
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Generate Professional CV
                  </>
                )}
              </Button>
              
              <div className="mt-3 text-center text-xs text-gray-500">
                Your CV will be generated based on the information provided and the template selected above
              </div>
            </div>
          </CardFooter>
        </Card>
      </div>
      
      <div className="mt-8 flex flex-col items-center w-full">
        {cvScore !== null && (
          <Card className="w-full mb-4">
            <CardHeader>
              <CardTitle>CV Match Score</CardTitle>
            </CardHeader>
            <CardContent>
              <span className="text-4xl font-bold">{cvScore}/100</span>
            </CardContent>
          </Card>
        )}
        {cvFeedback && (
          <Card className="w-full mb-4">
            <CardHeader>
              <CardTitle>AI Suggestions for Improvement</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-line">{cvFeedback}</p>
            </CardContent>
          </Card>
        )}
        {coverLetter && (
          <Card className="w-full">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Generated Cover Letter</CardTitle>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  navigator.clipboard.writeText(coverLetter);
                  toast({
                    title: "Copied!",
                    description: "Cover letter copied to clipboard.",
                  });
                }}
              >
                Copy
              </Button>
            </CardHeader>
            <CardContent>
              <Textarea
                value={coverLetter}
                readOnly
                className="min-h-[200px]"
              />
            </CardContent>
          </Card>
        )}
        
        {/* PDF Viewer Section */}
        {pdfUrl && (
          <Card className="w-full mt-4">
            <CardHeader>
              <CardTitle>Generated CV</CardTitle>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  if (pdfUrl) {
                    window.open(pdfUrl, "_blank");
                  }
                }}
              >
                Open in New Tab
              </Button>
            </CardHeader>
            <CardContent className="h-[600px]">
              <PDFViewer url={pdfUrl} />
            </CardContent>
          </Card>
        )}
        

      </div>
    </Tabs>
  );
};
