import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { PieChartData } from '@/services/dashboardService';

interface StatusPieChartProps {
  pieChartData: PieChartData;
}

export function StatusPieChart({ pieChartData }: StatusPieChartProps) {
  const data = pieChartData.labels.map((label, index) => ({
    name: label.charAt(0).toUpperCase() + label.slice(1),
    value: pieChartData.datasets[0].data[index],
  }));

  const colors = pieChartData.datasets[0].backgroundColor;

  return (
    <div style={{ width: '100%', height: 300 }}>
        <ResponsiveContainer>
            <PieChart>
                <Pie
                    data={data}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={(entry) => `${entry.name} (${entry.value})`}
                >
                    {data.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                    ))}
                </Pie>
                <Tooltip />
                <Legend />
            </PieChart>
        </ResponsiveContainer>
    </div>
  );
}
