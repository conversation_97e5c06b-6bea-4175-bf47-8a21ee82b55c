import { UpcomingReminder } from '@/services/dashboardService';
import { format } from 'date-fns';

interface UpcomingRemindersProps {
  reminders: UpcomingReminder[];
}

export function UpcomingReminders({ reminders }: UpcomingRemindersProps) {
  if (reminders.length === 0) {
    return <p className="text-sm text-muted-foreground">No upcoming reminders.</p>;
  }

  return (
    <div className="space-y-4">
      {reminders.map((reminder) => (
        <div key={reminder.id} className="flex items-center">
          <div className="ml-4 space-y-1">
            <p className="text-sm font-medium leading-none">{reminder.jobTitle}</p>
            <p className="text-sm text-muted-foreground">{reminder.company}</p>
          </div>
          <div className="ml-auto font-medium">
            {format(new Date(reminder.reminderDate), 'MMM dd')}
          </div>
        </div>
      ))}
    </div>
  );
}
