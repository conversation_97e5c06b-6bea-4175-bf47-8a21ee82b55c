import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/context/AuthContext";
import { Link } from "react-router-dom";
import { ArrowRight } from "lucide-react";

export const CallToAction = () => {
  const { isAuthenticated } = useAuth();

  return (
    <section className="w-full py-20 md:py-28 lg:py-32 relative overflow-hidden">
      {/* Background with gradient */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-secondary/10 to-background"></div>
        <div className="absolute bottom-0 left-0 w-full h-1/2 bg-gradient-to-t from-background to-transparent"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 rounded-full bg-primary/5 blur-3xl"></div>
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-secondary/5 blur-3xl"></div>
      </div>
      
      <div className="container px-4 md:px-6 relative">
        <div className="max-w-3xl mx-auto">
          <div className="bg-card/80 backdrop-blur-sm border shadow-lg rounded-2xl p-8 md:p-12 text-center">
            <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary mb-6">
              Ready to get started?
            </div>
            <h2 className="text-3xl md:text-4xl font-bold tracking-tighter mb-4">
              Transform your job search today
            </h2>
            <p className="text-muted-foreground text-lg md:text-xl max-w-2xl mx-auto mb-8">
              Join thousands of job seekers who have already boosted their career opportunities with JobHelperAI.
            </p>
            
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {isAuthenticated ? (
                <Button asChild size="lg" className="rounded-full px-8 h-12">
                  <Link to="/cv-generator" className="gap-2">
                    Create Your CV Now <ArrowRight className="h-4 w-4" />
                  </Link>
                </Button>
              ) : (
                <Button asChild size="lg" className="rounded-full px-8 h-12">
                  <Link to="/signup" className="gap-2">
                    Get Started for Free <ArrowRight className="h-4 w-4" />
                  </Link>
                </Button>
              )}
              
              <Button asChild variant="outline" size="lg" className="rounded-full px-8 h-12">
                <Link to="/login">
                  Learn More
                </Link>
              </Button>
            </div>
            
            <p className="text-sm text-muted-foreground mt-6">
              No credit card required. Free plan available with basic features.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};
