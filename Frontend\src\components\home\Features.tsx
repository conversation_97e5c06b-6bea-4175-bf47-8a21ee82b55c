
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText, MailOpen, BarChart3, Bo<PERSON> } from "lucide-react";

const features = [
  {
    title: "AI CV Generator",
    description: "Upload your CV or fill a form to generate a tailored CV for each job application in seconds.",
    icon: <FileText className="h-6 w-6" />,
    color: "bg-blue-500/10 text-blue-500",
  },
  {
    title: "Cover Letter Creator",
    description: "Generate personalized cover letters that highlight your relevant skills and experience for the job.",
    icon: <MailOpen className="h-6 w-6" />,
    color: "bg-purple-500/10 text-purple-500",
  },
  {
    title: "Application Tracker",
    description: "Keep track of all your job applications, set reminders, and never miss a follow-up.",
    icon: <BarChart3 className="h-6 w-6" />,
    color: "bg-green-500/10 text-green-500",
  },
  {
    title: "AI Apply Agent",
    description: "Set your job preferences and let our AI automatically find and apply to matching job opportunities.",
    icon: <Bot className="h-6 w-6" />,
    color: "bg-amber-500/10 text-amber-500",
  },
];

export const Features = () => {
  return (
    <section className="w-full py-20 md:py-28 lg:py-32 bg-muted/30 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/4 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-secondary/5 rounded-full blur-3xl"></div>
      </div>
      
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
          <div className="space-y-2 max-w-[800px]">
            <div className="inline-block rounded-full bg-muted-foreground/10 px-3 py-1 text-sm font-medium text-muted-foreground">Features</div>
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
              Everything you need to land your dream job
            </h2>
            <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl/relaxed">
              Our AI-powered platform streamlines your job search from application to interview.
            </p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="bg-card/50 backdrop-blur-sm border shadow transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
            >
              <CardHeader className="pb-2">
                <div className="flex items-center gap-4">
                  <div className={`p-2 rounded-lg ${feature.color}`}>
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {/* Feature highlight */}
        <div className="mt-20 bg-card/50 backdrop-blur-sm border rounded-xl p-8 shadow-lg">
          <div className="grid md:grid-cols-2 gap-10 items-center">
            <div className="space-y-4">
              <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm font-medium text-primary">
                How It Works
              </div>
              <h3 className="text-2xl font-bold">Powered by Advanced AI</h3>
              <p className="text-muted-foreground">
                Our platform uses cutting-edge AI technology to analyze job descriptions and your professional 
                profile to create perfectly tailored documents that increase your chances of landing interviews.
              </p>
              <ul className="space-y-2">
                {[
                  "Smart keyword optimization for ATS systems",
                  "Industry-specific templates and formatting",
                  "Personalized content based on job requirements",
                  "Professional tone and language enhancement"
                ].map((item, i) => (
                  <li key={i} className="flex items-center gap-2">
                    <span className="h-1.5 w-1.5 rounded-full bg-primary"></span>
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="relative h-[300px] rounded-lg overflow-hidden flex items-center justify-center">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 opacity-30"></div>
              <div className="w-full h-full flex items-center justify-center">
                <svg
                  className="w-40 h-40 text-primary/20"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17v-2h2v2h-2zm2-3h-2c0-3.25 3-3 3-5 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 2.5-3 2.75-3 5z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
