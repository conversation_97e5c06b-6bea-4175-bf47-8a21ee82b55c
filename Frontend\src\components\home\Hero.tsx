import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { ArrowRight, CheckCircle, ArrowUpRight } from "lucide-react";

export const Hero = () => {
  const { isAuthenticated } = useAuth();

  const benefits = [
    "Personalized CVs in seconds",
    "AI-generated cover letters",
    "Automatic job applications",
    "Smart application tracking"
  ];

  return (
    <section className="w-full py-20 md:py-32 lg:py-40 overflow-hidden relative">
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary/5 via-secondary/5 to-background"></div>
        <div className="absolute -top-[40%] -right-[30%] w-[70%] h-[70%] rounded-full bg-primary/5 blur-3xl"></div>
        <div className="absolute -bottom-[40%] -left-[30%] w-[70%] h-[70%] rounded-full bg-secondary/5 blur-3xl"></div>
      </div>
      
      <div className="container px-4 md:px-6 relative">
        <div className="grid gap-6 lg:grid-cols-[1fr_550px] lg:gap-12 xl:grid-cols-[1fr_650px] items-center">
          <div className="flex flex-col justify-center space-y-4">
            <div className="inline-block rounded-full bg-primary/10 px-4 py-1.5 text-sm font-medium text-primary w-fit mb-6 animate-fade-in">
              The smart way to land your dream job
            </div>
            
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-6xl/none animate-fade-in" style={{ animationDelay: "100ms" }}>
              <span className="text-gradient bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">Supercharge</span> Your Job Search with AI
            </h1>
            
            <p className="max-w-[600px] text-muted-foreground text-lg md:text-xl animate-fade-in" style={{ animationDelay: "200ms" }}>
              Use AI to create personalized CVs, generate compelling cover letters, track your applications, and apply to jobs automatically.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 min-[400px]:flex-row pt-6 animate-fade-in" style={{ animationDelay: "300ms" }}>
              {isAuthenticated ? (
                <Button asChild size="lg" className="px-8 h-12 rounded-full">
                  <Link to="/cv-generator" className="gap-2">
                    Get Started <ArrowRight className="h-4 w-4" />
                  </Link>
                </Button>
              ) : (
                <Button asChild size="lg" className="px-8 h-12 rounded-full">
                  <Link to="/signup" className="gap-2">
                    Sign Up Free <ArrowRight className="h-4 w-4" />
                  </Link>
                </Button>
              )}
              <Button asChild variant="outline" size="lg" className="px-8 h-12 rounded-full">
                <Link to="/login" className="gap-2">
                  Learn More <ArrowUpRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-10 animate-fade-in" style={{ animationDelay: "400ms" }}>
              {benefits.map((benefit, i) => (
                <div key={i} className="flex items-center gap-3">
                  <div className="rounded-full bg-primary/10 p-1 flex items-center justify-center">
                    <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                  </div>
                  <p className="text-muted-foreground font-medium">{benefit}</p>
                </div>
              ))}
            </div>
          </div>
          
          <div className="relative order-first lg:order-last">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-3xl blur-3xl opacity-20 -z-10"></div>
            <div className="relative bg-gradient-to-r from-card to-card/80 shadow-xl rounded-3xl border border-border/40 overflow-hidden">
              <div className="p-6 space-y-4">
                <div className="flex items-center gap-2 mb-8">
                  <div className="w-3 h-3 bg-destructive rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <div className="ml-4 text-xs text-muted-foreground">JobHelperAI Dashboard</div>
                </div>
                
                <div className="space-y-6">
                  {/* Dashboard UI mockup */}
                  <div className="flex gap-4">
                    <div className="bg-primary/5 rounded-lg h-24 w-24 flex items-center justify-center">
                      <div className="rounded-full bg-primary/10 p-2">
                        <svg className="h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                          <polyline points="14 2 14 8 20 8"></polyline>
                        </svg>
                      </div>
                    </div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-muted/60 rounded w-3/4"></div>
                      <div className="h-3 bg-muted/60 rounded w-1/2"></div>
                      <div className="h-3 bg-muted/60 rounded w-5/6"></div>
                    </div>
                  </div>
                  
                  <div className="bg-muted/30 p-4 rounded-lg space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="h-4 bg-muted/60 rounded w-1/3"></div>
                      <div className="h-6 bg-primary/20 rounded w-20"></div>
                    </div>
                    <div className="h-3 bg-muted/60 rounded w-full"></div>
                    <div className="h-3 bg-muted/60 rounded w-5/6"></div>
                    <div className="h-3 bg-muted/60 rounded w-4/6"></div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-muted/30 rounded-lg p-4 h-32 flex flex-col justify-between">
                      <div className="h-4 bg-muted/60 rounded w-2/3"></div>
                      <div className="text-3xl font-bold text-primary">4</div>
                      <div className="h-2 bg-muted/60 rounded w-full"></div>
                    </div>
                    <div className="bg-muted/30 rounded-lg p-4 h-32 flex flex-col justify-between">
                      <div className="h-4 bg-muted/60 rounded w-2/3"></div>
                      <div className="text-3xl font-bold text-secondary">12</div>
                      <div className="h-2 bg-muted/60 rounded w-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Decorative elements */}
            <div className="absolute -bottom-6 -left-10 w-20 h-20 bg-primary/10 rounded-full blur-xl"></div>
            <div className="absolute -top-6 -right-10 w-20 h-20 bg-secondary/10 rounded-full blur-xl"></div>
          </div>
        </div>
      </div>
    </section>
  );
  };