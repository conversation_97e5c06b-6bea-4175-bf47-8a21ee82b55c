
import { <PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const testimonials = [
  {
    name: "<PERSON>",
    role: "Software Developer",
    content: "JobHelperAI helped me land my dream job at a top tech company. The CV generator and personalized cover letters made my applications stand out.",
    avatar: "SJ",
  },
  {
    name: "<PERSON>",
    role: "Marketing Specialist",
    content: "I was applying to jobs for months with no callbacks. After using JobHelperAI, I got 5 interview invitations in just two weeks!",
    avatar: "<PERSON>",
  },
  {
    name: "<PERSON>",
    role: "UX Designer",
    content: "The job tracker feature is a game-changer. I no longer miss follow-ups, and the AI-generated application materials saved me so much time.",
    avatar: "ER",
  },
  {
    name: "<PERSON>",
    role: "Data Analyst",
    content: "As someone who struggles with writing cover letters, this tool is invaluable. It captures my experience perfectly and tailors it to each position.",
    avatar: "DT",
  },
];

export const Testimonials = () => {
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-background">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm">Testimonials</div>
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">
              Trusted by job seekers worldwide
            </h2>
            <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              See what our users are saying about their experience with JobHelperAI.
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-2 lg:gap-12 pt-12">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="bg-muted/30">
              <CardContent className="pt-6">
                <p className="text-muted-foreground">{testimonial.content}</p>
              </CardContent>
              <CardFooter>
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src={`/avatars/${index + 1}.png`} alt={testimonial.name} />
                    <AvatarFallback>{testimonial.avatar}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium leading-none">{testimonial.name}</p>
                    <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                  </div>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};
