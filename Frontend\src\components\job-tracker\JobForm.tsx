
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";

export const JobForm = ({ onAddJob }: { onAddJob: (job: any) => void }) => {
  const [jobData, setJobData] = useState({
    title: "",
    company: "",
    location: "",
    url: "",
    description: "",
    appliedDate: new Date().toISOString().split("T")[0],
    status: "Applied",
    notes: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setJobData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Add a small delay to simulate API call
    setTimeout(() => {
      const newJob = {
        ...jobData,
        id: Date.now().toString(),
        remindDate: null,
      };
      
      onAddJob(newJob);
      
      toast({
        title: "Job Added",
        description: "The job application has been added to your tracker.",
      });
      
      // Reset form
      setJobData({
        title: "",
        company: "",
        location: "",
        url: "",
        description: "",
        appliedDate: new Date().toISOString().split("T")[0],
        status: "Applied",
        notes: "",
      });
      
      setIsSubmitting(false);
    }, 500);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Add Job Application</CardTitle>
        <CardDescription>
          Track a new job application you've submitted
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Job Title</Label>
              <Input
                id="title"
                name="title"
                value={jobData.title}
                onChange={handleChange}
                placeholder="Software Engineer"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company">Company</Label>
              <Input
                id="company"
                name="company"
                value={jobData.company}
                onChange={handleChange}
                placeholder="Acme Inc."
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                name="location"
                value={jobData.location}
                onChange={handleChange}
                placeholder="New York, NY (Remote)"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="url">Job URL</Label>
              <Input
                id="url"
                name="url"
                type="url"
                value={jobData.url}
                onChange={handleChange}
                placeholder="https://company.com/jobs/12345"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="appliedDate">Date Applied</Label>
              <Input
                id="appliedDate"
                name="appliedDate"
                type="date"
                value={jobData.appliedDate}
                onChange={handleChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                name="status"
                value={jobData.status}
                onChange={handleChange}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="Applied">Applied</option>
                <option value="In Progress">In Progress</option>
                <option value="Interview">Interview</option>
                <option value="Offer">Offer</option>
                <option value="Rejected">Rejected</option>
                <option value="Withdrawn">Withdrawn</option>
              </select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Job Description</Label>
            <Textarea
              id="description"
              name="description"
              value={jobData.description}
              onChange={handleChange}
              placeholder="Brief description of the job..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              name="notes"
              value={jobData.notes}
              onChange={handleChange}
              placeholder="Any additional notes about this application..."
            />
          </div>
        </CardContent>
        <CardFooter>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Adding..." : "Add Job Application"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
};
