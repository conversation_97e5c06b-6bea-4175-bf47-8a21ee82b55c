import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  <PERSON><PERSON>Footer,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { AlarmClock, FileText, Edit, MoreVertical, Trash2 } from "lucide-react";
import { JobUpdateForm } from "./JobUpdateForm";

type Job = {
  id: string;
  title: string;
  company: string;
  location: string;
  url: string;
  appliedDate: string;
  status: string;
  description: string;
  notes: string;
  remindDate: string | null;
};

export const JobTable = ({
  jobs,
  onUpdateJob,
  onDeleteJob,
}: {
  jobs: Job[];
  onUpdateJob: (job: Job) => void;
  onDeleteJob: (jobId: string) => void;
}) => {
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [jobToUpdate, setJobToUpdate] = useState<Job | null>(null);
  const [isUpdateSheetOpen, setIsUpdateSheetOpen] = useState(false);
  const [remindDate, setRemindDate] = useState<string>("");
  const { toast } = useToast();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const handleSetReminder = () => {
    if (selectedJob && remindDate) {
      const updatedJob = { ...selectedJob, remindDate };
      onUpdateJob(updatedJob);

      toast({
        title: "Reminder Set",
        description: `You will be reminded about "${
          selectedJob.title
        }" on ${formatDate(remindDate)}.`,
      });

      setSelectedJob(null);
      setRemindDate("");
    }
  };

  const handleUpdateClick = (job: Job) => {
    setJobToUpdate(job);
    setIsUpdateSheetOpen(true);
  };

  const handleUpdateCancel = () => {
    setJobToUpdate(null);
    setIsUpdateSheetOpen(false);
  };

  const handleUpdateJob = (updatedJob: Job) => {
    onUpdateJob(updatedJob);
    setIsUpdateSheetOpen(false);
    setJobToUpdate(null);
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Applied":
        return "default";
      case "In Progress":
        return "secondary";
      case "Interview":
        return "primary";
      case "Offer":
        return "success";
      case "Rejected":
        return "destructive";
      case "Withdrawn":
        return "outline";
      default:
        return "default";
    }
  };

  return (
    <div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Job Title</TableHead>
              <TableHead>Company</TableHead>
              <TableHead>Date Applied</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Reminder</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {jobs.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={6}
                  className="text-center py-8 text-muted-foreground"
                >
                  No job applications yet. Add your first one!
                </TableCell>
              </TableRow>
            ) : (
              jobs.map((job) => (
                <TableRow key={job.id}>
                  <TableCell className="font-medium">{job.title}</TableCell>
                  <TableCell>{job.company}</TableCell>
                  <TableCell>{formatDate(job.appliedDate)}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(job.status) as any}>
                      {job.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {job.remindDate ? formatDate(job.remindDate) : "None"}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 p-0"
                        >
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <Dialog>
                          <DialogTrigger asChild>
                            <DropdownMenuItem
                              onSelect={(e) => {
                                e.preventDefault();
                                setSelectedJob(job);
                              }}
                            >
                              <AlarmClock className="mr-2 h-4 w-4" />
                              <span>Set Reminder</span>
                            </DropdownMenuItem>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>
                                Set Reminder for {job.title}
                              </DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4 py-4">
                              <div className="space-y-2">
                                <p className="text-sm text-muted-foreground">
                                  Select a date to be reminded about this
                                  application:
                                </p>
                                <Input
                                  type="date"
                                  value={remindDate}
                                  onChange={(e) =>
                                    setRemindDate(e.target.value)
                                  }
                                  min={new Date().toISOString().split("T")[0]}
                                />
                              </div>
                            </div>
                            <DialogFooter>
                              <Button onClick={handleSetReminder}>
                                Set Reminder
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>

                        <Sheet>
                          <SheetTrigger asChild>
                            <DropdownMenuItem
                              onSelect={(e) => {
                                e.preventDefault();
                              }}
                            >
                              <FileText className="mr-2 h-4 w-4" />
                              <span>View Details</span>
                            </DropdownMenuItem>
                          </SheetTrigger>
                          <SheetContent>
                            <SheetHeader>
                              <SheetTitle>
                                {job.title} at {job.company}
                              </SheetTitle>
                              <SheetDescription>
                                Application details and description
                              </SheetDescription>
                            </SheetHeader>
                            <div className="mt-6 space-y-4">
                              <div>
                                <h4 className="text-sm font-medium">Status</h4>
                                <p>
                                  <Badge
                                    variant={
                                      getStatusBadgeVariant(job.status) as any
                                    }
                                  >
                                    {job.status}
                                  </Badge>
                                </p>
                              </div>
                              <div>
                                <h4 className="text-sm font-medium">
                                  Applied Date
                                </h4>
                                <p>{formatDate(job.appliedDate)}</p>
                              </div>
                              <div>
                                <h4 className="text-sm font-medium">
                                  Location
                                </h4>
                                <p>{job.location || "Not specified"}</p>
                              </div>
                              <div>
                                <h4 className="text-sm font-medium">
                                  Job Description
                                </h4>
                                <p className="whitespace-pre-line text-sm text-muted-foreground">
                                  {job.description || "No description provided"}
                                </p>
                              </div>
                              <div>
                                <h4 className="text-sm font-medium">Notes</h4>
                                <p className="whitespace-pre-line text-sm text-muted-foreground">
                                  {job.notes || "No notes"}
                                </p>
                              </div>
                              <div>
                                <h4 className="text-sm font-medium">Job URL</h4>
                                <p className="break-all text-sm">
                                  {job.url ? (
                                    <a
                                      href={job.url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-blue-500 hover:underline"
                                    >
                                      {job.url}
                                    </a>
                                  ) : (
                                    "No URL provided"
                                  )}
                                </p>
                              </div>
                            </div>
                          </SheetContent>
                        </Sheet>

                        <DropdownMenuSeparator />

                        <DropdownMenuItem
                          onSelect={(e) => {
                            e.preventDefault();
                            handleUpdateClick(job);
                          }}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          <span>Update Job</span>
                        </DropdownMenuItem>                        <DropdownMenuItem
                          onSelect={(e) => {
                            e.preventDefault();
                            onDeleteJob(job.id);
                          }}
                          className="text-destructive focus:text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          <span>Delete Job</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Update Job Sheet */}
      <Sheet open={isUpdateSheetOpen} onOpenChange={setIsUpdateSheetOpen}>
        <SheetContent className="sm:max-w-md md:max-w-lg overflow-y-auto">
          <SheetHeader>
            <SheetTitle>Update Job Application</SheetTitle>
            <SheetDescription>
              Make changes to your job application details
            </SheetDescription>
          </SheetHeader>
          <div className="py-6">
            {jobToUpdate && (
              <JobUpdateForm
                job={jobToUpdate}
                onUpdate={handleUpdateJob}
                onCancel={handleUpdateCancel}
              />
            )}
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};
