import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";

export type Job = {
  id: string;
  title: string;
  company: string;
  location: string;
  url: string;
  appliedDate: string;
  status: string;
  description: string;
  notes: string;
  remindDate: string | null;
};

type JobUpdateFormProps = {
  job: Job;
  onUpdate: (job: Job) => void;
  onCancel: () => void;
};

export const JobUpdateForm = ({
  job,
  onUpdate,
  onCancel,
}: JobUpdateFormProps) => {
  const [jobData, setJobData] = useState<Job>({ ...job });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    setJobData({ ...job });
  }, [job]);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setJobData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Add a small delay to simulate API call
    setTimeout(() => {
      onUpdate(jobData);

      toast({
        title: "Job Updated",
        description: "The job application has been updated successfully.",
      });

      setIsSubmitting(false);
    }, 500);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="update-title">Job Title</Label>
          <Input
            id="update-title"
            name="title"
            value={jobData.title}
            onChange={handleChange}
            placeholder="Software Engineer"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="update-company">Company</Label>
          <Input
            id="update-company"
            name="company"
            value={jobData.company}
            onChange={handleChange}
            placeholder="Acme Inc."
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="update-location">Location</Label>
          <Input
            id="update-location"
            name="location"
            value={jobData.location}
            onChange={handleChange}
            placeholder="New York, NY (Remote)"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="update-url">Job URL</Label>
          <Input
            id="update-url"
            name="url"
            type="url"
            value={jobData.url}
            onChange={handleChange}
            placeholder="https://company.com/jobs/12345"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="update-appliedDate">Date Applied</Label>
          <Input
            id="update-appliedDate"
            name="appliedDate"
            type="date"
            value={jobData.appliedDate}
            onChange={handleChange}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="update-status">Status</Label>
          <select
            id="update-status"
            name="status"
            value={jobData.status}
            onChange={handleChange}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <option value="Applied">Applied</option>
            <option value="In Progress">In Progress</option>
            <option value="Interview">Interview</option>
            <option value="Offer">Offer</option>
            <option value="Rejected">Rejected</option>
            <option value="Withdrawn">Withdrawn</option>
          </select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="update-description">Job Description</Label>
        <Textarea
          id="update-description"
          name="description"
          value={jobData.description}
          onChange={handleChange}
          placeholder="Brief description of the job..."
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="update-notes">Notes</Label>
        <Textarea
          id="update-notes"
          name="notes"
          value={jobData.notes}
          onChange={handleChange}
          placeholder="Any additional notes about this application..."
        />
      </div>

      <div className="flex justify-end space-x-2">
        <Button variant="outline" type="button" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Updating..." : "Update Job Application"}
        </Button>
      </div>
    </form>
  );
};
