
import { Link } from "react-router-dom";

export const Footer = () => {
  const year = new Date().getFullYear();

  return (
    <footer className="w-full py-6 border-t">
      <div className="container flex flex-col md:flex-row items-center justify-between gap-4">
        <div className="flex flex-col items-center md:items-start">
          <Link to="/" className="text-xl font-semibold text-gradient">
            JobHelperAI
          </Link>
          <p className="text-sm text-muted-foreground">
            Empowering your job search with AI
          </p>
        </div>
        
        <div className="flex flex-col md:flex-row items-center gap-4 md:gap-8">
          <nav className="flex gap-4 md:gap-6">
            <Link to="/" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Home
            </Link>
            <Link to="/cv-generator" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              CV Generator
            </Link>
            <Link to="/job-tracker" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Job Tracker
            </Link>
            <Link to="/ai-apply" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              AI Apply
            </Link>
          </nav>
          
          <div className="h-4 w-px bg-border hidden md:block" />
          
          <div className="flex gap-4">
            <a href="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Privacy
            </a>
            <a href="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
              Terms
            </a>
          </div>
        </div>
        
        <div className="text-sm text-muted-foreground">
          &copy; {year} JobHelperAI. All rights reserved.
        </div>
      </div>
    </footer>
  );
};
