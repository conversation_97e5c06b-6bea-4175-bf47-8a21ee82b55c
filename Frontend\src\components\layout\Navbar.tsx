import { But<PERSON> } from "@/components/ui/button";
import { useTheme } from "@/context/ThemeContext";
import { useAuth } from "@/context/AuthContext";
import { Link } from "react-router-dom";
import { <PERSON>, <PERSON>, Menu, X, User } from "lucide-react";
import { useState } from "react";
import { ProfileModal } from "./ProfileModal";

export const Navbar = () => {
  const { theme, toggleTheme } = useTheme();
  const { user, logout, isAuthenticated, isLoading: isAuthLoading } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);

  return (
    <>
      <header className="sticky top-0 z-50 w-full border-b bg-background/80 backdrop-blur-sm">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-6">
            <Link to="/" className="flex items-center gap-2">
              <span className="text-2xl font-bold text-gradient">JobHelperAI</span>
            </Link>
            
            {/* Desktop Navigation */}
            <nav className="hidden md:flex gap-6">
              <Link to="/" className="text-sm font-medium hover:text-primary transition-colors">
                Home
              </Link>
              {isAuthenticated && (
                <>
                  <Link to="/dashboard" className="text-sm font-medium hover:text-primary transition-colors">
                    Dashboard
                  </Link>
                  <Link to="/cv-generator" className="text-sm font-medium hover:text-primary transition-colors">
                    CV Generator
                  </Link>
                  <Link to="/job-tracker" className="text-sm font-medium hover:text-primary transition-colors">
                    Job Tracker
                  </Link>
                  <Link to="/ai-apply" className="text-sm font-medium hover:text-primary transition-colors">
                    AI Apply
                  </Link>
                </>
              )}
            </nav>
          </div>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              aria-label="Toggle theme"
              onClick={toggleTheme}
            >
              {theme === 'dark' ? <Sun /> : <Moon />}
            </Button>

            {isAuthLoading ? (
              null
            ) : isAuthenticated ? (
              <div className="flex items-center gap-4">
                {user?.name && <span className="text-sm font-medium">{user.name}</span>}
                <Button
                  variant="ghost"
                  size="icon"
                  aria-label="Open profile"
                  onClick={() => setIsProfileModalOpen(true)}
                >
                  <User className="h-5 w-5" />
                </Button>
                <Button variant="outline" onClick={logout}>
                  Logout
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Button variant="outline" asChild>
                  <Link to="/login">Login</Link>
                </Button>
                <Button asChild>
                  <Link to="/signup">Signup</Link>
                </Button>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? <X /> : <Menu />}
          </Button>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden py-4 px-6 space-y-4 bg-background border-b animate-slide-down">
            <nav className="flex flex-col space-y-4">
              <Link 
                to="/"
                className="text-sm font-medium hover:text-primary transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                Home
              </Link>
              {isAuthenticated && (
                <>
                  <Link to="/dashboard" className="text-sm font-medium hover:text-primary transition-colors" onClick={() => setMobileMenuOpen(false)}>Dashboard</Link>
                  <Link to="/cv-generator" className="text-sm font-medium hover:text-primary transition-colors" onClick={() => setMobileMenuOpen(false)}>CV Generator</Link>
                  <Link to="/job-tracker" className="text-sm font-medium hover:text-primary transition-colors" onClick={() => setMobileMenuOpen(false)}>Job Tracker</Link>
                  <Link to="/ai-apply" className="text-sm font-medium hover:text-primary transition-colors" onClick={() => setMobileMenuOpen(false)}>AI Apply</Link>
                  <a
                    href="#"
                    className="text-sm font-medium hover:text-primary transition-colors"
                    onClick={(e) => {
                      e.preventDefault();
                      setIsProfileModalOpen(true);
                      setMobileMenuOpen(false);
                    }}
                  >
                    Profile
                  </a>
                </>
              )}
            </nav>
            <div className="border-t pt-4">
              {isAuthLoading ? null : isAuthenticated ? (
                <Button className="w-full" variant="outline" onClick={() => { logout(); setMobileMenuOpen(false); }}>Logout</Button>
              ) : (
                <div className="flex items-center gap-2">
                  <Button className="flex-1" variant="outline" asChild><Link to="/login" onClick={() => setMobileMenuOpen(false)}>Login</Link></Button>
                  <Button className="flex-1" asChild><Link to="/signup" onClick={() => setMobileMenuOpen(false)}>Signup</Link></Button>
                </div>
              )}
            </div>
          </div>
        )}
      </header>
      <ProfileModal isOpen={isProfileModalOpen} onOpenChange={setIsProfileModalOpen} />
    </>
  );
};
