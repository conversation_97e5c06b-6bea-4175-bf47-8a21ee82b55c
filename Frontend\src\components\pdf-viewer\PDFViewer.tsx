import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface PDFViewerProps {
  pdfData?: string;
  url?: string;
}

export const PDFViewer: React.FC<PDFViewerProps> = ({ pdfData, url }) => {
  const [objectUrl, setObjectUrl] = useState<string | null>(null);

  useEffect(() => {
    // If binary PDF data is provided
    if (pdfData) {
      try {
        // Convert base64 string to blob
        const byteCharacters = atob(pdfData);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/pdf' });
        
        // Create URL from blob
        const url = URL.createObjectURL(blob);
        setObjectUrl(url);
        
        // Clean up URL on unmount
        return () => {
          URL.revokeObjectURL(url);
        };
      } catch (error) {
        console.error('Error creating PDF blob:', error);
      }
    }
  }, [pdfData]);

  return (
    <Card>
      <CardContent className="p-0">
        <iframe
          src={objectUrl || url || 'about:blank'}
          className="w-full h-[600px] border-0"
          title="PDF Viewer"
        />
      </CardContent>
    </Card>
  );
};
