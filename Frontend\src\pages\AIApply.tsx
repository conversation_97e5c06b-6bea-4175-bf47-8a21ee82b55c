import { useState } from "react";
import { PreferencesForm } from "@/components/ai-apply/PreferencesForm";
import { StatusPanel } from "@/components/ai-apply/StatusPanel";
import { useAuth } from "@/context/AuthContext";
import { Navigate } from "react-router-dom";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { searchJobsWithAI } from "@/services/apiAI";

const AIApply = () => {
  const { isAuthenticated, isLoading: isAuthLoading, user } = useAuth();
  const [isAgentActive, setIsAgentActive] = useState(false);
  const [preferences, setPreferences] = useState<any>(null);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  if (isAuthLoading) {
    return null;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  const handleActivateAgent = async (prefs: any) => {
    setPreferences(prefs);
    setIsSearching(true);
    setSearchError(null);
    setSearchResults([]);
    setIsAgentActive(true); 

    try {
      const jobs = await searchJobsWithAI(prefs);
      setSearchResults(jobs);
      
    } catch (error: any) {
      setSearchError(error.message || "Failed to search for jobs.");
    } finally {
      setIsSearching(false);
    }
  };

  const handleDeactivateAgent = () => {
    setIsAgentActive(false);
    setSearchResults([]); 
    setSearchError(null);
    setPreferences(null);
  };

  return (
    <div className="container max-w-screen-xl mx-auto py-12">
      <div className="space-y-8">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">AI Apply Agent</h1>
          <p className="text-muted-foreground">
            Let our AI automatically find and apply to jobs that match your preferences.
          </p>
        </div>

        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Demo Mode</AlertTitle>
          <AlertDescription>
            This is a demonstration of the AI Apply Agent. In a real implementation, this would connect to job boards and apply on your behalf.
          </AlertDescription>
        </Alert>

        {isAgentActive ? (
          <StatusPanel 
            active={isAgentActive} 
            onDeactivate={handleDeactivateAgent} 
            preferences={preferences} 
            searchResults={searchResults}
            isSearching={isSearching}
            searchError={searchError}
          />
        ) : (
          <PreferencesForm onActivate={handleActivateAgent} />
        )}
      </div>
    </div>
  );
};

export default AIApply;
