import { CVForm } from "@/components/cv-generator/CVForm";
import { useAuth } from "@/context/AuthContext";
import { Navigate } from "react-router-dom";

const CVGenerator = () => {
  const { isAuthenticated, isLoading: isAuthLoading } = useAuth();

  if (isAuthLoading) {
    return null;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className="container max-w-screen-xl mx-auto py-12">
      <div className="flex flex-col space-y-4">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">CV & Cover Letter Generator</h1>
          <p className="text-muted-foreground">
            Generate a personalized CV and cover letter tailored to specific job descriptions.
          </p>
        </div>
        <CVForm />
      </div>
    </div>
  );
};

export default CVGenerator;
