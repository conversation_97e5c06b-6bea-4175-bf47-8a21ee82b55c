import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getDashboardData } from '../services/dashboardService';
import { Button } from '@/components/ui/button';
import { StatsCards } from '@/components/dashboard/StatsCards';
import { StatusPieChart } from '@/components/dashboard/StatusPieChart';
import { UpcomingReminders } from '@/components/dashboard/UpcomingReminders';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const periods = [
  { label: 'Last 7 Days', value: '7' },
  { label: 'Last 30 Days', value: '30' },
  { label: 'Last 90 Days', value: '90' },
  { label: 'All Time', value: 'all' },
];

export function DashboardPage() {
  const [period, setPeriod] = useState('all');

  const { data, isLoading, error } = useQuery({
    queryKey: ['dashboardData', period],
    queryFn: () => getDashboardData(period),
  });

  return (
    <div className="container mx-auto p-4 md:p-8 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <div className="flex items-center space-x-2">
          {periods.map((p) => (
            <Button
              key={p.value}
              variant={period === p.value ? 'default' : 'outline'}
              onClick={() => setPeriod(p.value)}
            >
              {p.label}
            </Button>
          ))}
        </div>
      </div>

      {isLoading && <p>Loading dashboard...</p>}
      {error && <p className="text-red-500">Error: {error.message}</p>}

      {data && (
        <div className="space-y-6">
          <StatsCards stats={data.stats} />
          <div className="grid gap-6 md:grid-cols-3">
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Application Status</CardTitle>
              </CardHeader>
              <CardContent>
                 <StatusPieChart pieChartData={data.stats.pieChartData} />
              </CardContent>
            </Card>
            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle>Upcoming Reminders</CardTitle>
              </CardHeader>
              <CardContent>
                <UpcomingReminders reminders={data.reminders} />
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}
