import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { JobForm } from "@/components/job-tracker/JobForm";
import { JobTable } from "@/components/job-tracker/JobTable";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/context/AuthContext";
import { Navigate } from "react-router-dom";
import { addJob } from "@/services/apiJobs"; 
import { getAllJobs } from "@/services/apiJobs"; 
import { updateJob } from "@/services/apiJobs";
import { deleteJob } from "@/services/apiJobs";
import { useToast } from "@/hooks/use-toast";

const JobTracker = () => {
  const { isAuthenticated, user, isLoading: isAuthLoading } = useAuth();
  const queryClient = useQueryClient(); 
  const [jobToEdit, setJobToEdit] = useState<any | null>(null);
  const { toast } = useToast();

  const { data: jobs = [], isLoading: isLoadingJobs, error: fetchError } = useQuery<any[], Error>({
    queryKey: ['jobs', user?.id],
    queryFn: () => getAllJobs(user!.id!), 
    enabled: !!user && !!user.id, 
  });

  const addJobMutation = useMutation<any, Error, any>({
    mutationFn: (newJobData) => addJob(newJobData, user!.id!), 
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobs', user?.id] });
    },
  });

  const updateJobMutation = useMutation<any, Error, any>({
    mutationFn: (updatedJobData) => updateJob(updatedJobData.id, updatedJobData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobs', user?.id] });
      setJobToEdit(null); 
    },
  });  const deleteJobMutation = useMutation<any, Error, string>({
    mutationFn: (jobId: string) => deleteJob(jobId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobs', user?.id] });
      toast({
        title: "Job Deleted",
        description: "The job application has been successfully deleted.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete job application. Please try again.",
        variant: "destructive",
      });
    },
  });

  if (isAuthLoading) {
    return null; // Render nothing while auth is loading
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  const handleAddJob = async (job: any) => {
    if (!user || !user.id) {
      console.error("User not authenticated or user ID not found. Cannot add job.");
      return;
    }
    addJobMutation.mutate(job);
  };

  const handleUpdateJob = async (job: any) => {
    updateJobMutation.mutate(job);
  };
  const handleDeleteJob = async (jobId: string) => {
    const jobToDelete = jobs.find(job => job.id === jobId);
    const jobTitle = jobToDelete?.title || 'this job';
    
    if (window.confirm(`Are you sure you want to delete "${jobTitle}"? This action cannot be undone.`)) {
      deleteJobMutation.mutate(jobId);
    }
  };

  const displayError = fetchError || addJobMutation.error || updateJobMutation.error || deleteJobMutation.error;

  return (
    <div className="container max-w-screen-xl mx-auto py-12">
      <div className="space-y-8">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">Job Application Tracker</h1>
          <p className="text-muted-foreground">
            Keep track of all your job applications and set reminders for
            follow-ups.
          </p>
        </div>
        {displayError && <p className="text-red-500">Error: {displayError.message}</p>}
        <Tabs defaultValue="applications" className="w-full">
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="applications">Applications</TabsTrigger>
            <TabsTrigger value="add">Add New</TabsTrigger>
          </TabsList>
          <div className="mt-6">
            <TabsContent value="applications">
              {isLoadingJobs && jobs.length === 0 ? (
                  <div className="flex justify-center items-center h-32">
                    <p className="text-muted-foreground">Loading applications...</p>
                  </div>
                ) : (
                  <JobTable 
                    jobs={jobs} 
                    onUpdateJob={handleUpdateJob} 
                    onDeleteJob={handleDeleteJob} 
                  />
                )}
            </TabsContent>            <TabsContent value="add">
              <JobForm onAddJob={handleAddJob} />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
};

export default JobTracker;
