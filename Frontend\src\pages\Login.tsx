
import { LoginForm } from "@/components/auth/LoginForm";
import { useAuth } from "@/context/AuthContext";
import { Navigate } from "react-router-dom";

const Login = () => {
  const { isAuthenticated } = useAuth();

  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  return (
    
    <div className="container max-w-screen-xl mx-auto py-12 md:py-24">
      <LoginForm />
    </div>
  );
};

export default Login;
