
import { SignupForm } from "@/components/auth/SignupForm";
import { useAuth } from "@/context/AuthContext";
import { Navigate } from "react-router-dom";

const Signup = () => {
  const { isAuthenticated } = useAuth();

  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="container max-w-screen-xl mx-auto py-12 md:py-24">
      <SignupForm />
    </div>
  );
};

export default Signup;
