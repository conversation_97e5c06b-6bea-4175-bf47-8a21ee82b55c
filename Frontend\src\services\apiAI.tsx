// Define a more specific type for preferences if possible
interface AIJobPreferences {
  jobTitles?: string;
  locations?: string;
  keywords?: string;
  excludeKeywords?: string;
  experience?: string;
  remoteOnly?: boolean;
  // Add any other preference fields that are expected by the backend
}

// Define a type for the expected job result structure
interface AIJobSearchResult {
  id: string;
  title: string;
  company: string;
  location: string;
  url?: string;
  // Add other relevant fields returned by the API
}

interface AISearchResponse {
  jobs: AIJobSearchResult[];
}

const API_BASE_URL = 'http://localhost:5000'; // Your backend API base URL, e.g., http://localhost:5000 if not proxying

export const searchJobsWithAI = async (preferences: AIJobPreferences): Promise<AIJobSearchResult[]> => {
  const response = await fetch(`${API_BASE_URL}/ai/search-jobs`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      // Include Authorization header if your AI endpoints require authentication
      // 'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(preferences),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: "An unknown error occurred" })); // Graceful error handling for non-JSON responses
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }

  const data: AISearchResponse = await response.json();
  return data.jobs || [];
};
