// Use environment variables in a real app, hardcoded for development
const API_URL = "http://127.0.0.1:5000/api/cv";

// Common fetch options for CORS
const fetchOptions = {
  mode: 'cors' as RequestMode,
  credentials: 'omit' as RequestCredentials,  // Don't send credentials since our backend doesn't require them
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
};

/**
 * Get available CV templates from the backend
 * @returns List of available template names
 */
export const getTemplates = async (): Promise<string[]> => {
  try {
    const response = await fetch(`${API_URL}/templates`, {
      method: "GET",
      ...fetchOptions,
    });

    if (!response.ok) {
      throw new Error(`Error: ${response.status}`);
    }

    const data = await response.json();
    return data.templates || [];
  } catch (error) {
    console.error("Error fetching templates:", error);
    return [];
  }
};

/**
 * Generate a CV in PDF format using LaTeX
 * @param cvData CV data
 * @param jobDescription Job description for tailoring
 * @param templateName Template name to use
 * @param enhanceWithAI Whether to enhance with AI
 * @returns PDF blob
 */
export const generateCV = async (
  cvData: any,
  jobDescription: string,
  templateName: string = "modern",
  enhanceWithAI: boolean = true
): Promise<Blob> => {
  try {
    const response = await fetch(`${API_URL}/generate`, {
      method: "POST",
      ...fetchOptions,
      body: JSON.stringify({
        cv_data: cvData,
        job_description: jobDescription,
        template_name: templateName,
        enhance_with_ai: enhanceWithAI,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Error ${response.status}`);
    }

    return await response.blob();
  } catch (error) {
    console.error("Error generating CV:", error);
    throw error;
  }
};

/**
 * Generate LaTeX code for the CV
 * @param cvData CV data
 * @param jobDescription Job description for tailoring
 * @param templateName Template name to use
 * @param enhanceWithAI Whether to enhance with AI
 * @returns LaTeX content
 */
export const generateLatexCV = async (
  cvData: any,
  jobDescription: string,
  templateName: string = "modern",
  enhanceWithAI: boolean = true
): Promise<string> => {
  try {
    const response = await fetch(`${API_URL}/generate-latex`, {
      method: "POST",
      ...fetchOptions,
      body: JSON.stringify({
        cv_data: cvData,
        job_description: jobDescription,
        template_name: templateName,
        enhance_with_ai: enhanceWithAI,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Error ${response.status}`);
    }

    const data = await response.json();
    return data.latex || "";
  } catch (error) {
    console.error("Error generating LaTeX:", error);
    throw error;
  }
};
