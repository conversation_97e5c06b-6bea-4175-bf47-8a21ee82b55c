import { auth } from "../lib/firebase";

const API_URL = import.meta.env.VITE_API_BASE_URL ? `${import.meta.env.VITE_API_BASE_URL}/jobs` : "http://127.0.0.1:5000/jobs";

// Helper function to get authorization headers
const getAuthHeaders = async (includeContentType = true) => {
  const user = auth.currentUser;
  if (!user) {
    throw new Error("User not authenticated to perform this action.");
  }
  const token = await user.getIdToken();
  const headers: Record<string, string> = {
    'Authorization': `Bearer ${token}`,
  };
  if (includeContentType) {
    headers['Content-Type'] = 'application/json';
  }
  return headers;
};

// Method to add a new job application
export const addJob = async (job: any) => {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_URL}/new-application`, {
      method: "POST",
      headers,
      body: JSON.stringify(job),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to add job application");
    }

    const result = await response.json();
    return result;
  } catch (err) {
    throw new Error((err as Error).message);
  }
};

// Method to fetch all job applications for the current user
export const getAllJobs = async () => {
  try {
    const headers = await getAuthHeaders(false); // No 'Content-Type' for GET
    const response = await fetch(`${API_URL}/all-applications`, {
      method: "GET",
      headers,
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch job applications");
    }

    const result = await response.json();
    return result.jobs;
  } catch (err) {
    throw new Error((err as Error).message);
  }
};

// Method to fetch a single job application by ID
export const getJobById = async (jobId: string) => {
  try {
    const headers = await getAuthHeaders(false); // No 'Content-Type' for GET
    const response = await fetch(`${API_URL}/${jobId}`, {
      method: "GET",
      headers,
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch job application by ID");
    }

    const result = await response.json();
    return result.job;
  } catch (err) {
    throw new Error((err as Error).message);
  }
};

// Method to update a job application
export const updateJob = async (jobId: string, updatedJob: any) => {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_URL}/${jobId}`, {
      method: "PUT",
      headers,
      body: JSON.stringify(updatedJob),
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update job application");
    }

    const result = await response.json();
    return result;
  } catch (err) {
    throw new Error((err as Error).message);
  }
};

// Method to delete a job application by ID
export const deleteJob = async (jobId: string) => {
  try {
    const headers = await getAuthHeaders(false); // No 'Content-Type' for DELETE
    const response = await fetch(`${API_URL}/${jobId}`, {
      method: "DELETE",
      headers,
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete job application");
    }

    const result = await response.json();
    return result;
  } catch (err) {
    throw new Error((err as Error).message);
  }
};
