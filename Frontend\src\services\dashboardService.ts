import { auth } from "../lib/firebase";

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:5000';

export interface PieChartData {
  labels: string[];
  datasets: {
    data: number[];
    backgroundColor: string[];
  }[];
}

export interface DashboardStats {
  totalApplications: number;
  statusCounts: Record<string, number>;
  pieChartData: PieChartData;
}

export interface UpcomingReminder {
  id: string;
  jobTitle: string;
  company: string;
  reminderDate: string;
}

export interface DashboardData {
  stats: DashboardStats;
  reminders: UpcomingReminder[];
}

// --- MOCK DATA GENERATOR ---
const getMockDashboardData = (period: string): DashboardData => {
  console.log(`Fetching MOCK dashboard data for period: ${period}`);
  const statusCounts = {
    saved: Math.floor(Math.random() * 10) + 10,
    pending: Math.floor(Math.random() * 5) + 5,
    interviewing: Math.floor(Math.random() * 4),
    offered: Math.floor(Math.random() * 2),
    rejected: Math.floor(Math.random() * 5),
  };

  const totalApplications = Object.values(statusCounts).reduce((a, b) => a + b, 0);

  return {
    stats: {
      totalApplications,
      statusCounts,
      pieChartData: {
        labels: Object.keys(statusCounts),
        datasets: [{
          data: Object.values(statusCounts),
          backgroundColor: ['#9966FF', '#36A2EB', '#FFCE56', '#4BC0C0', '#FF6384'],
        }],
      },
    },
    reminders: [
      { id: '1', jobTitle: 'Frontend Developer', company: 'Tech Solutions Inc.', reminderDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString() },
      { id: '2', jobTitle: 'Backend Engineer', company: 'Data Systems', reminderDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString() },
      { id: '3', jobTitle: 'Full-Stack Engineer', company: 'Innovate Corp', reminderDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() },
    ],
  };
};

export const getDashboardData = async (period: string): Promise<DashboardData> => {
  // Simulate network delay to show loading state
  await new Promise(resolve => setTimeout(resolve, 500));
  return getMockDashboardData(period);

  /*
  // --- REAL API CALL (uncomment when Firestore index is ready) ---
  const user = auth.currentUser;
  if (!user) {
    throw new Error("User not authenticated");
  }

  const token = await user.getIdToken();

  const response = await fetch(`${API_BASE_URL}/api/dashboard/stats?period=${period}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to fetch dashboard data');
  }

  return response.json();
  */
};
