import { auth } from "../lib/firebase";

const API_URL = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:5000';

export interface UserProfile {
  firstName: string;
  lastName: string;
  headline: string;
  email: string; // Email is read-only from the token
}

// Helper to get auth headers
const getAuthHeaders = async (includeContentType = true) => {
  const user = auth.currentUser;
  if (!user) {
    throw new Error("User not authenticated.");
  }
  const token = await user.getIdToken();
  const headers: Record<string, string> = {
    'Authorization': `Bearer ${token}`,
  };
  if (includeContentType) {
    headers['Content-Type'] = 'application/json';
  }
  return headers;
};

// Fetch the user's profile
export const getUserProfile = async (): Promise<UserProfile> => {
  const headers = await getAuthHeaders(false);
  const response = await fetch(`${API_URL}/api/user/profile`, {
    method: 'GET',
    headers,
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to fetch user profile');
  }

  return response.json();
};

// Update the user's profile
export const updateUserProfile = async (profileData: Partial<UserProfile>): Promise<any> => {
  const headers = await getAuthHeaders();
  const response = await fetch(`${API_URL}/api/user/profile`, {
    method: 'PUT',
    headers,
    body: JSON.stringify(profileData),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to update user profile');
  }

  return response.json();
};

// Change user password
export const changePassword = async (password: string): Promise<any> => {
  const headers = await getAuthHeaders();
  const response = await fetch(`${API_URL}/api/user/profile/change-password`, {
    method: 'POST',
    headers,
    body: JSON.stringify({ password }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to change password');
  }

  return response.json();
};
