/**
 * Uses AI to generate a custom cover letter based on CV data and a job description.
 * @param {object} cvData - The flat CV data object (form fields).
 * @param {string} jobDescription - The job offer/description text.
 * @returns {Promise<string>} - The generated cover letter as plain text.
 */
export async function generateCoverLetterAI(
  cvData: any,
  jobDescription: string
): Promise<string> {
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY || "<GEMINI_API_KEY_HERE>";
  const endpoint = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

  const prompt = `You are an expert career assistant. Write a professional, personalized cover letter for the following job, using the candidate's CV data. The letter should be ready to copy and use, tailored to the job, and highlight the most relevant experience and skills.\n\nCV Data (JSON):\n${JSON.stringify(
    cvData,
    null,
    2
  )}\n\nJob Description:\n${jobDescription}\n\nReturn only the cover letter as plain text, no markdown, no explanation.`;

  const requestBody = {
    contents: [
      {
        parts: [{ text: prompt }],
      },
    ],
  };

  const response = await fetch(endpoint, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) throw new Error("Failed to generate cover letter");
  const data = await response.json();
  let result = data.candidates?.[0]?.content?.parts?.[0]?.text || "";
  // Remove markdown code block if present
  result = result.replace(/^```[a-z]*\s*/i, "").replace(/```\s*$/i, "");
  return result.trim();
}
