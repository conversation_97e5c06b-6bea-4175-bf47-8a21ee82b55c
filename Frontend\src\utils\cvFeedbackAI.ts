/**
 * Uses AI to provide feedback and a score for a CV based on the job description.
 * @param {object} cvData - The flat CV data object (form fields).
 * @param {string} jobDescription - The job offer/description text.
 * @returns {Promise<{ feedback: string, score: number }>} - Feedback and a match score (0-100).
 */
export async function getCVFeedbackAndScore(
  cvData: any,
  jobDescription: string
): Promise<{ feedback: string; score: number }> {
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY || "<GEMINI_API_KEY_HERE>";
  const endpoint = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

  const prompt = `You are a career coach AI. Given the following CV data and job description, provide:
1. Constructive feedback on how the CV matches the job and what to improve or focus on.
2. A match score (0-100) for how well the CV fits the job.

CV Data (JSON):\n${JSON.stringify(
    cvData,
    null,
    2
  )}\n\nJob Description:\n${jobDescription}\n\nReturn a JSON object like: { feedback: string, score: number } (no markdown, no explanation).`;

  const requestBody = {
    contents: [
      {
        parts: [{ text: prompt }],
      },
    ],
  };

  const response = await fetch(endpoint, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) throw new Error("Failed to get CV feedback and score");
  const data = await response.json();
  let result = data.candidates?.[0]?.content?.parts?.[0]?.text || "";
  result = result.replace(/^```json\s*/i, "").replace(/```\s*$/i, "");
  return JSON.parse(result);
}
