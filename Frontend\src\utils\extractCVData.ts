/**
 * Extracts structured CV data from plain text using Google Gemini API.
 * @param {string} text - The extracted text from the uploaded CV (PDF/DOC/DOCX).
 * @returns {Promise<Object>} - The structured CV data matching the frontend form fields.
 *
 * The returned JSON will be:
 * {
 *   name: string, // Full name
 *   email: string, // Email address
 *   phone: string, // Phone number
 *   summary: string, // Professional summary
 *   experience: string, // Work experience as a single string
 *   education: string, // Education as a single string
 *   skills: string // Comma-separated skills
 * }
 */
export async function extractCVDataFromText(text: string) {
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY || "<GEMINI_API_KEY_HERE>";
  const endpoint = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

  const schemaDescription = `\nReturn a JSON object matching this schema:\n{\n  name,\n  email,\n  phone,\n  summary,\n  experience,\n  education,\n  skills\n}\nWhere:\n- name: Full name (string)\n- email: Email address (string)\n- phone: Phone number (string)\n- summary: Professional summary (string)\n- experience: Work experience as a single string, including job titles, companies, dates, and key responsibilities (string)\n- education: Education background as a single string, including degrees, institutions, and graduation dates (string)\n- skills: Relevant skills as a comma-separated string (string)\nOnly return valid JSON, no markdown or explanation.`;

  const requestBody = {
    contents: [
      {
        parts: [
          {
            text: `Extract the following CV content into the described JSON schema. ${schemaDescription}\n\n${text}`,
          },
        ],
      },
    ],
  };

  const response = await fetch(endpoint, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) throw new Error("Failed to extract structured CV data");
  const data = await response.json();
  let result = data.candidates?.[0]?.content?.parts?.[0]?.text || "";
  // Remove markdown code block if present
  result = result.replace(/^```json\s*/i, "").replace(/```\s*$/i, "");
  // Parse and return the flat object for the form
  return JSON.parse(result);
}
