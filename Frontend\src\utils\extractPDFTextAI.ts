/**
 * Extracts text from a PDF file using Google Gemini API (text-only models do NOT support PDF/image input).
 * This function is for demonstration and will NOT work with gemini-2.0-flash endpoint for PDF files.
 * For real PDF extraction, use pdfjs-dist for text-based PDFs or a proper OCR API for scanned PDFs.
 *
 * @param {File} file - The PDF file to extract text from.
 * @returns {Promise<string>} - The extracted text.
 */
export async function extractPdfText(file: File): Promise<string> {
  // Read the file as base64
  const toBase64 = (file: File) =>
    new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve((reader.result as string).split(",")[1]);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  const base64 = await toBase64(file);

  console.log(file.name);

  // Prepare the Gemini API endpoint and key
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY || "<GEMINI_API_KEY_HERE>";
  // NOTE: gemini-2.0-flash does NOT support PDF/image input. This is for reference only.
  // For actual PDF/image input, you need a vision-enabled endpoint (not public as of June 2025).
  const endpoint = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

  // Prepare the request body for Gemini Vision
  const requestBody = {
    contents: [
      {
        parts: [
          {
            inlineData: {
              mimeType: "application/pdf",
              data: base64,
            },
          },
          {
            text: "Extract all readable text from this PDF file. Only return the plain text, no formatting, no explanation.",
          },
        ],
      },
    ],
  };

  // Call Gemini Vision API
  const response = await fetch(endpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    throw new Error("Failed to extract PDF text with Gemini Vision API");
  }
  const data = await response.json();
  // Extract the text from the response
  const result = data.candidates?.[0]?.content?.parts?.[0]?.text || "";
  // Notify user via console only (no UI styling changes)
  console.log("PDF Extraction Successful");
  return result;
}
