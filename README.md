# JOB-AI Project

(Add a brief description of your project here.)

## Prerequisites

Before you begin, ensure you have the following installed:
- [Node.js](https://nodejs.org/) (which includes npm)
- [Python](https://www.python.org/)
- pip (Python package installer, usually comes with Python)
- **For PDF Generation features on Windows:**
    - MiKTeX:
        - Go to the [MiKTeX download page](https://miktex.org/download).
        - Download the installer for Windows.
        - Run the installer and follow the setup wizard.
        - During installation, select *Install missing packages on-the-fly: Yes*.
        - After installation, restart your terminal or IDE.

## Setup

1.  **Clone the repository (if you haven't already):**
    ```bash
    git clone https://github.com/ENSET-PI/JOB-AI
    cd JOB-AI
    ```

2.  **Backend Setup:**
    Navigate to the `Backend` directory:
    ```bash
    cd Backend
    ```
    Create a virtual environment (recommended):
    ```bash
    python -m venv venv
    ```
    Activate the virtual environment:
    -   Windows:
        ```bash
        .\venv\Scripts\activate
        ```
    -   macOS/Linux:
        ```bash
        source venv/bin/activate
        ```
    Install the required Python packages:
    ```bash
    pip install -r requirements.txt
    ```
    (If you have a `.env` file for environment variables, ensure it's configured correctly in the `Backend` directory.)

3.  **Frontend Setup:**
    Navigate to the `Frontend` directory (from the project root):
    ```bash
    cd Frontend
    ```
    Install the Node.js dependencies:
    ```bash
    npm install
    ```

## Running the Application

### 1. Start the Backend Server

-   Ensure you are in the `Backend` directory (`cd Backend` from the project root).
-   If you created a virtual environment, make sure it's activated.
-   Run the application:
    ```bash
    python run.py
    ```
    The backend server should now be running. Note the port it's running on (usually specified in the application or `.env` file).

### 2. Start the Frontend Development Server

-   Open a new terminal or navigate from the project root to the `Frontend` directory (`cd Frontend`).
-   Run the development server:
    ```bash
    npm run dev
    ```
    The frontend development server will start, typically on a port like `http://localhost:5173` (Vite's default). Open this URL in your web browser.

## Additional Notes

-   (Add any other relevant information here, e.g., build instructions, deployment, specific configurations, etc.)
