# ilyas - Project Progress Log

## What Has Been Done So Far

### 1. CV Upload and Extraction

- Implemented a CV upload feature in the frontend (PDF support).
- Added logic to extract text from uploaded PDF files using AI (Gemini Vision API).
- Added user notifications for file upload and PDF extraction status.

### 2. AI-Powered CV Data Extraction

- Created a utility to send extracted CV text to Gemini API and receive structured JSON matching the frontend form fields.
- Auto-filled the **CV form** with the extracted data.

### 3. AI Feedback and Scoring

- Added a button to get AI feedback and a match score for the CV against a provided job description.
- Displayed the score and suggestions in the UI using the app's default Card components.
- Blocked feedback/cover letter generation until PDF extraction is complete, with user notifications.

### 4. AI Cover Letter Generation

- Added a button to generate a custom cover letter using AI, based on the CV data and job description.
- Displayed the generated cover letter in its own section, with a copy-to-clipboard button and notification.

---

## What Is Left / Next Steps

- **Generate a new CV (PDF) based on the improved data:**
  - Use the AI feedback and suggestions to enhance the CV data.
  - Generate a new CV in LaTeX format (using a template), incorporating the improvements.
  - Render and export the LaTeX code as a downloadable PDF for the user.
- **(Optional) Add a button to download the improved CV as PDF.**
- **(Optional) Allow user to edit the improved CV before generating the final PDF.**

---

