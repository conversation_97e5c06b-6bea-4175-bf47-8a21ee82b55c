# JOB-AI Chrome Extension

## Overview
A Chrome extension that allows users to extract job offer information directly from job listing pages and send that data to the JOB-AI backend for analysis and cover letter generation.

## Features
- 🔐 **Firebase Authentication** - Secure login with email/password
- 🎯 **Smart Text Selection** - Grammarly-style widget for job description extraction
- 🤖 **AI-Powered Parsing** - Automatic extraction of job title, company, skills, etc.
- 📊 **JOB-AI Integration** - Direct connection to your JOB-AI dashboard
- 🌐 **Multi-Site Support** - Works on LinkedIn, Indeed, and other job sites

## Installation

### Development Mode
1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked" and select the `job-ai-extension` folder
4. The extension should now appear in your extensions bar

### Production (when published)
- Install from Chrome Web Store (coming soon)

## Usage

### First Time Setup
1. Click the JOB-AI extension icon in your browser toolbar
2. Sign in with your JOB-AI account credentials
3. If you don't have an account, click "Sign up" to create one

### Extracting Job Information
1. Navigate to any job listing page (LinkedIn, Indeed, etc.)
2. Select the job description text with your mouse
3. A JOB-AI widget will appear near your selection
4. Click "Extract Job Info" to automatically parse the information
5. Review and edit the extracted fields if needed
6. Click "Send to JOB-AI" to save the job to your dashboard

### Alternative Methods
- **Right-click Context Menu**: Select text and choose "Extract Job Info with JOB-AI"
- **Page Analysis**: Right-click anywhere and choose "Analyze Page for Job Info"

## Supported Job Sites
- LinkedIn Jobs
- Indeed
- Glassdoor
- Monster
- ZipRecruiter
- CareerBuilder
- Google Jobs
- Stack Overflow Jobs
- AngelList/Wellfound
- And many more...

## Technical Details

### Architecture
- **Manifest V3** Chrome extension
- **Firebase Authentication** for user management
- **Content Scripts** for webpage interaction
- **Background Service Worker** for API communication
- **Popup Interface** for authentication and settings

### Files Structure
```
job-ai-extension/
├── manifest.json              # Extension configuration
├── popup/                     # Extension popup UI
│   ├── popup.html
│   ├── popup.css
│   └── popup.js
├── content/                   # Content scripts for web pages
│   ├── content.js
│   └── content.css
├── background/                # Background service worker
│   └── background.js
├── config/                    # Configuration files
│   └── firebase-config.js
└── assets/                    # Extension icons and assets
```

## Development

### Prerequisites
- Chrome Browser (latest version)
- JOB-AI Backend running locally or deployed
- Firebase project with Authentication enabled

### Local Development
1. Clone the JOB-AI repository
2. Navigate to the `job-ai-extension` folder
3. Update the backend URL in `background.js` and `content.js`
4. Load the extension in Chrome developer mode
5. Test on various job sites

### Configuration
Update the following files for your environment:
- `config/firebase-config.js` - Firebase project settings
- `background/background.js` - Backend API URL
- `content/content.js` - Backend API URL

## API Integration

The extension communicates with the JOB-AI backend using:
- **Authentication**: Firebase ID tokens
- **Endpoint**: `POST /api/jobs`
- **Headers**: `Authorization: Bearer <firebase-token>`

### Job Data Format
```json
{
  "title": "Software Engineer",
  "company": "Tech Corp",
  "location": "San Francisco, CA",
  "skills": "JavaScript, React, Node.js",
  "description": "Full job description text...",
  "sourceUrl": "https://example.com/job/123",
  "extractedAt": "2025-06-09T10:30:00Z"
}
```

## Troubleshooting

### Common Issues
1. **Extension not appearing**: Ensure developer mode is enabled
2. **Authentication fails**: Check Firebase configuration
3. **Widget not showing**: Refresh the page and try selecting text again
4. **Backend errors**: Verify the backend URL and authentication

### Debug Mode
1. Open Chrome DevTools
2. Go to Extensions tab
3. Find JOB-AI Extension and click "Inspect views: background"
4. Check console for error messages

## Privacy & Security
- ✅ Only processes selected text, not entire pages
- ✅ Secure Firebase authentication
- ✅ Encrypted communication with backend
- ✅ No data stored locally except auth tokens
- ✅ Permissions requested only for necessary features

## Roadmap
- [ ] Enhanced AI extraction capabilities
- [ ] Support for more job sites
- [ ] Bulk job extraction
- [ ] Job alerts and notifications
- [ ] Chrome Web Store publication

## Support
For issues or questions:
1. Check the troubleshooting section above
2. Open an issue in the JOB-AI repository
3. Contact the development team

## License
Part of the JOB-AI project - see main repository for license details.
