// JOB-AI Extension Background Script
// Handles extension lifecycle, context menus, and background tasks

class JobAIBackground {
    constructor() {
        this.initialize();
    }

    initialize() {
        // Set up event listeners
        this.setupEventListeners();
        
        // Create context menu
        this.createContextMenu();
        
        console.log('JOB-AI background script initialized');
    }

    setupEventListeners() {
        // Extension installation/update
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstallation(details);
        });

        // Context menu clicks
        chrome.contextMenus.onClicked.addListener((info, tab) => {
            this.handleContextMenuClick(info, tab);
        });

        // Message handling from content scripts and popup
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Tab updates (for detecting job sites)
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });
    }

    handleInstallation(details) {
        if (details.reason === 'install') {
            console.log('JOB-AI Extension installed');
            
            // Open welcome page or popup
            this.showWelcomeMessage();
            
        } else if (details.reason === 'update') {
            console.log('JOB-AI Extension updated');
        }
    }

    createContextMenu() {
        // Remove existing context menus
        chrome.contextMenus.removeAll(() => {
            // Create context menu for selected text
            chrome.contextMenus.create({
                id: 'jobai-extract-selection',
                title: 'Extract Job Info with JOB-AI',
                contexts: ['selection'],
                documentUrlPatterns: ['https://*/*', 'http://*/*']
            });

            // Create context menu for page analysis
            chrome.contextMenus.create({
                id: 'jobai-analyze-page',
                title: 'Analyze Page for Job Info',
                contexts: ['page'],
                documentUrlPatterns: ['https://*/*', 'http://*/*']
            });
        });
    }

    async handleContextMenuClick(info, tab) {
        try {
            switch (info.menuItemId) {
                case 'jobai-extract-selection':
                    await this.extractFromSelection(info, tab);
                    break;
                
                case 'jobai-analyze-page':
                    await this.analyzePage(tab);
                    break;
            }
        } catch (error) {
            console.error('Context menu click error:', error);
        }
    }

    async extractFromSelection(info, tab) {
        if (!info.selectionText) return;

        try {
            // Check authentication
            const isAuthenticated = await this.checkAuthentication();
            if (!isAuthenticated) {
                this.showAuthenticationRequired();
                return;
            }

            // Inject content script if not already present
            await this.ensureContentScript(tab.id);

            // Send selection to content script
            await chrome.tabs.sendMessage(tab.id, {
                action: 'extract-selection',
                text: info.selectionText,
                pageUrl: tab.url
            });

        } catch (error) {
            console.error('Selection extraction error:', error);
        }
    }

    async analyzePage(tab) {
        try {
            // Check authentication
            const isAuthenticated = await this.checkAuthentication();
            if (!isAuthenticated) {
                this.showAuthenticationRequired();
                return;
            }

            // Inject content script if not already present
            await this.ensureContentScript(tab.id);

            // Send page analysis request
            await chrome.tabs.sendMessage(tab.id, {
                action: 'analyze-page',
                pageUrl: tab.url
            });

        } catch (error) {
            console.error('Page analysis error:', error);
        }
    }

    async ensureContentScript(tabId) {
        try {
            // Try to ping the content script
            await chrome.tabs.sendMessage(tabId, { action: 'ping' });
        } catch (error) {
            // Content script not present, inject it
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content/content.js']
            });

            await chrome.scripting.insertCSS({
                target: { tabId: tabId },
                files: ['content/content.css']
            });
        }
    }    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'get-auth-status':
                    const isAuthenticated = await this.checkAuthentication();
                    sendResponse({ authenticated: isAuthenticated });
                    break;                case 'parse-only':
                    const parseResult = await this.parseJobOnly(message.data);
                    sendResponse({ 
                        success: parseResult.success, 
                        parsedResult: parseResult.data,
                        error: parseResult.error 
                    });
                    break;

                case 'save-to-backend':
                    const saveResult = await this.saveToBackend(message.data);
                    sendResponse({ success: saveResult });
                    break;

                case 'send-to-backend':
                    const result = await this.sendToBackend(message.data);
                    sendResponse({ success: result });
                    break;

                case 'show-notification':
                    this.showNotification(message.text, message.type);
                    sendResponse({ success: true });
                    break;

                case 'open-dashboard':
                    this.openDashboard();
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Message handling error:', error);
            sendResponse({ error: error.message });
        }
    }

    handleTabUpdate(tabId, changeInfo, tab) {
        // Detect job sites and show page action if needed
        if (changeInfo.status === 'complete' && tab.url) {
            const isJobSite = this.isJobSite(tab.url);
            
            if (isJobSite) {
                // Could show a page action or notification
                this.showJobSiteDetected(tabId);
            }
        }
    }

    isJobSite(url) {
        const jobSites = [
            'linkedin.com',
            'indeed.com',
            'glassdoor.com',
            'monster.com',
            'ziprecruiter.com',
            'careerbuilder.com',
            'jobs.google.com',
            'stackoverflow.com',
            'angel.co',
            'wellfound.com'
        ];

        return jobSites.some(site => url.includes(site));
    }    async checkAuthentication() {
        try {
            const result = await chrome.storage.local.get(['jobai_auth_token', 'jobai_auth_timestamp']);
            
            if (!result.jobai_auth_token) return false;
            
            // Check if token is not too old (1 hour)
            const now = Date.now();
            const tokenAge = now - (result.jobai_auth_timestamp || 0);
            const maxAge = 60 * 60 * 1000; // 1 hour
            
            return tokenAge < maxAge;
        } catch (error) {
            console.error('Authentication check error:', error);
            return false;
        }
    }

    async getAuthToken() {
        try {
            const result = await chrome.storage.local.get(['jobai_auth_token']);
            return result.jobai_auth_token || null;
        } catch (error) {
            console.error('Get auth token error:', error);
            return null;
        }
    }async sendToBackend(jobData) {
        try {
            console.log('Sending job data to RAG parser:', jobData);
            
            const result = await chrome.storage.local.get(['jobai_auth_token']);
            const authToken = result.jobai_auth_token;
            
            if (!authToken) {
                throw new Error('No authentication token');
            }

            // Use your Flask backend URL
            const backendUrl = 'http://localhost:5000'; 
            
            // First, use RAG parser to extract structured job information
            const parseData = {
                text: jobData.description || jobData.text || '',
                url: jobData.sourceUrl || jobData.url || jobData.pageUrl || ''
            };
            
            console.log('Parsing job offer with RAG:', parseData);
            
            const parseResponse = await fetch(`${backendUrl}/jobs/parse-offer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify(parseData)
            });

            if (!parseResponse.ok) {
                const error = await parseResponse.text();
                throw new Error(`RAG parser error: ${error}`);
            }

            const parsedData = await parseResponse.json();
            console.log('RAG parsed result:', parsedData);
            
            // If parsing was successful, save the structured data to Firestore
            if (parsedData && !parsedData.error) {
                // The RAG parser already returns data in the correct format
                // Just ensure we have the URL included
                const finalData = {
                    ...parsedData,
                    url: parseData.url,
                    extractedBy: 'chrome-extension-rag',
                    extractedAt: new Date().toISOString()
                };
                
                const saveResponse = await fetch(`${backendUrl}/jobs/new-application`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(finalData)
                });

                if (saveResponse.ok) {
                    const savedData = await saveResponse.json();
                    return { 
                        success: true, 
                        data: finalData,
                        message: 'Job successfully parsed with AI and saved to database',
                        parsedResult: parsedData
                    };
                } else {
                    const error = await saveResponse.text();
                    throw new Error(`Save error: ${error}`);
                }
            } else {
                throw new Error(`RAG parsing failed: ${parsedData.error || 'Unknown error'}`);
            }

        } catch (error) {
            console.error('Backend send error:', error);
            return { success: false, error: error.message };
        }
    }

    showAuthenticationRequired() {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'assets/icon48.png',
            title: 'JOB-AI Authentication Required',
            message: 'Please log in to the JOB-AI extension to use this feature.'
        });
    }

    showNotification(message, type = 'basic') {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'assets/icon48.png',
            title: 'JOB-AI',
            message: message
        });
    }

    showJobSiteDetected(tabId) {
        // Could set a badge or show an indicator
        chrome.action.setBadgeText({
            tabId: tabId,
            text: '●'
        });

        chrome.action.setBadgeBackgroundColor({
            color: '#3b82f6'
        });    }

    async parseJobOnly(jobData) {
        try {
            const authToken = await this.getAuthToken();
            if (!authToken) {
                throw new Error('No authentication token');
            }
            
            const backendUrl = 'http://localhost:5000';
            
            // Prepare data for RAG parsing only
            const parseData = {
                text: jobData.text || jobData.description || '',
                description: jobData.description || jobData.text || '',
                url: jobData.sourceUrl || jobData.url || jobData.pageUrl || ''
            };
            
            console.log('Parsing job offer with RAG (no save):', parseData);
            
            const parseResponse = await fetch(`${backendUrl}/jobs/parse-offer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify(parseData)
            });

            if (!parseResponse.ok) {
                const error = await parseResponse.text();
                throw new Error(`RAG parser error: ${error}`);
            }

            const parsedData = await parseResponse.json();
            console.log('RAG parsed result (extract only):', parsedData);
            
            return {
                success: true,
                data: parsedData,
                message: 'Job information extracted successfully'
            };
            
        } catch (error) {
            console.error('Parse-only error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async saveToBackend(jobData) {
        try {
            const authToken = await this.getAuthToken();
            if (!authToken) {
                throw new Error('No authentication token');
            }
            
            const backendUrl = 'http://localhost:5000';
            
            // Transform data to match backend's expected format
            const transformedData = {
                title: jobData.title || 'Unknown Title',
                company: jobData.company || 'Unknown Company',
                location: jobData.location || '',
                url: jobData.url || jobData.sourceUrl || '',
                description: jobData.description || '',
                appliedDate: jobData.appliedDate || new Date().toISOString().split('T')[0],
                status: jobData.status || 'Applied',
                notes: jobData.notes || '',
                extractedAt: jobData.extractedAt || new Date().toISOString(),
                extractedBy: jobData.extractedBy || 'chrome-extension'
            };
            
            console.log('Saving job to backend:', transformedData);
            
            const response = await fetch(`${backendUrl}/jobs/new-application`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify(transformedData)
            });

            if (response.ok) {
                const savedData = await response.json();
                console.log('Job saved successfully:', savedData);
                return {
                    success: true,
                    data: savedData,
                    message: 'Job application saved successfully'
                };
            } else {
                const error = await response.text();
                throw new Error(`Save error: ${error}`);
            }
            
        } catch (error) {
            console.error('Save-to-backend error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    showWelcomeMessage() {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'assets/icon48.png',
            title: 'Welcome to JOB-AI Extension!',
            message: 'Click on the extension icon to get started with job extraction.'
        });
    }    openDashboard() {
        // Open JOB-AI web dashboard in a new tab
        chrome.tabs.create({
            url: 'http://localhost:3000' // Adjust to your frontend URL
        });
    }

    showNotification(text, type = 'basic') {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'assets/icon48.png',
            title: type === 'error' ? 'JOB-AI Error' : 'JOB-AI',
            message: text
        });
    }

    // Utility method to get stored auth data
    static async getAuthData() {
        try {
            const result = await chrome.storage.local.get([
                'jobai_user_id',
                'jobai_user_email',
                'jobai_auth_token',
                'jobai_auth_timestamp'
            ]);
            return result;
        } catch (error) {
            console.error('Error getting auth data:', error);
            return {};
        }
    }

    // Utility method to clear all extension data
    static async clearAllData() {
        try {
            await chrome.storage.local.clear();
            console.log('All extension data cleared');
        } catch (error) {
            console.error('Error clearing data:', error);
        }
    }
}

// Initialize background script
const jobAIBackground = new JobAIBackground();
