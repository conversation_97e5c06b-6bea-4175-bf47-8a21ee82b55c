/* JOB-AI Extension Content Styles */
/* This file is injected into web pages to style the extraction widget */

/* Reset styles for the widget to prevent conflicts with host page styles */
#jobai-extraction-widget * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* Widget container styles are defined in content.js for better encapsulation */
/* This file can be used for any additional content script styling */

/* Selection highlight enhancement */
::selection {
    background-color: rgba(59, 130, 246, 0.2) !important;
}

::-moz-selection {
    background-color: rgba(59, 130, 246, 0.2) !important;
}

/* Prevent conflicts with host page styles */
#jobai-extraction-widget {
    all: initial;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
}

/* Ensure widget appears above all other content */
#jobai-extraction-widget {
    z-index: 2147483647 !important; /* Maximum z-index value */
}
