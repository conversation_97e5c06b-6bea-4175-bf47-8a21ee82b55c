// JOB-AI Extension Content Script
// Handles text selection, widget display, and job extraction

class JobAIContentScript {
    constructor() {
        this.selectedText = '';
        this.selectionRange = null;
        this.widget = null;
        this.isWidgetVisible = false;

        this.initialize();
    }

    initialize() {
        // Only run on job-related sites or allow on all sites
        if (this.isJobSite() || true) { // Allow on all sites for now
            this.setupSelectionListener();
            this.createWidget();
            this.setupMessageListener();
            console.log('JOB-AI content script initialized');
        }
    }

    setupMessageListener() {
        // Listen for messages from background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'ping':
                    sendResponse({ success: true });
                    break;

                case 'extract-selection':
                    await this.handleExtractSelection(message);
                    sendResponse({ success: true });
                    break;

                case 'analyze-page':
                    await this.handleAnalyzePage(message);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Content script message handling error:', error);
            sendResponse({ error: error.message });
        }
    }

    async handleExtractSelection(message) {
        const { text, pageUrl } = message;

        // Prepare job data with selection and URL
        const jobData = {
            text: text,
            description: text,
            sourceUrl: pageUrl,
            url: pageUrl,
            pageUrl: pageUrl,
            extractedBy: 'context-menu',
            extractedAt: new Date().toISOString()
        };

        // Send to background script for processing
        await this.sendJobDataToBackground(jobData);
    }

    async handleAnalyzePage(message) {
        const { pageUrl } = message;

        // Try to extract job content from the page
        const pageContent = this.extractPageContent();

        // Prepare job data with page content and URL
        const jobData = {
            text: pageContent,
            description: pageContent,
            sourceUrl: pageUrl,
            url: pageUrl,
            pageUrl: pageUrl,
            extractedBy: 'page-analysis',
            extractedAt: new Date().toISOString()
        };

        // Send to background script for processing
        await this.sendJobDataToBackground(jobData);
    }

    extractPageContent() {
        // Try to find job-related content on the page
        const selectors = [
            '[data-job-description]',
            '.job-description',
            '.job-details',
            '.job-content',
            '.posting-description',
            '.description',
            'article',
            'main',
            '.content'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim().length > 100) {
                return element.textContent.trim();
            }
        }

        // Fallback to body text if no specific job content found
        return document.body.textContent.trim().substring(0, 5000);
    }

    async sendJobDataToBackground(jobData) {
        try {
            // Send to background script for RAG processing
            const response = await chrome.runtime.sendMessage({
                action: 'send-to-backend',
                data: jobData
            });

            if (response.success) {
                this.showNotification('Job information extracted and saved successfully!', 'success');
            } else {
                this.showNotification('Failed to extract job information', 'error');
            }
        } catch (error) {
            console.error('Error sending job data to background:', error);
            this.showNotification('Error processing job data', 'error');
        }
    }

    isJobSite() {
        const hostname = window.location.hostname.toLowerCase();
        const jobSites = [
            'linkedin.com',
            'indeed.com',
            'glassdoor.com',
            'monster.com',
            'ziprecruiter.com',
            'careerbuilder.com',
            'jobs.google.com',
            'stackoverflow.com',
            'angel.co',
            'wellfound.com'
        ];

        return jobSites.some(site => hostname.includes(site));
    }    setupSelectionListener() {
        document.addEventListener('mouseup', (e) => this.handleTextSelection(e));
        document.addEventListener('keyup', (e) => this.handleTextSelection(e));

        // REMOVED: Click outside to close behavior - only close button closes widget now
        // This eliminates the clicking inside widget issue completely
    }    handleTextSelection(e) {
        // Don't handle text selection if clicking inside the widget
        if (this.widget && this.isWidgetVisible) {
            let target = e.target;
            while (target) {
                if (target === this.widget || target.id === 'jobai-extraction-widget') {
                    console.log('Click inside widget - ignoring text selection');
                    return; // Don't process text selection if clicking inside widget
                }
                target = target.parentNode;
            }
        }

        setTimeout(() => {
            const selection = window.getSelection();
            const selectedText = selection.toString().trim();

            if (selectedText.length > 50) { // Minimum text length for job descriptions
                this.selectedText = selectedText;
                this.selectionRange = selection.getRangeAt(0);
                this.showWidget(e);
            } else {
                // Only hide widget if it's not currently visible or if we're not clicking inside it
                if (this.isWidgetVisible) {
                    console.log('Text selection too short, but widget is visible - keeping open');
                } else {
                    this.hideWidget();
                }
            }
        }, 100);
    }

    createWidget() {
        // Create widget container
        this.widget = document.createElement('div');
        this.widget.id = 'jobai-extraction-widget';
        this.widget.innerHTML = this.getWidgetHTML();

        // Apply styles
        this.applyWidgetStyles();

        // Hide initially
        this.widget.style.display = 'none';

        // Add to page
        document.body.appendChild(this.widget);

        // Setup widget event listeners
        this.setupWidgetListeners();
    }

    getWidgetHTML() {
        return `
            <div class="jobai-widget-container">
                <div class="jobai-widget-header">
                    <div class="jobai-widget-logo">
                        <span class="jobai-logo-text">JOB-AI</span>
                    </div>
                    <button class="jobai-widget-close" id="jobai-close-btn">×</button>
                </div>
                
                <div class="jobai-widget-content">
                    <div class="jobai-extraction-status">
                        <div class="jobai-status-icon">🎯</div>
                        <div class="jobai-status-text">
                            <strong>Job Description Detected</strong>
                            <p>Extract and analyze this job posting</p>
                        </div>
                    </div>                    <div class="jobai-extracted-info" id="jobai-extracted-info" style="display: none;">
                        <h4>Extracted Information:</h4>
                        <div class="jobai-info-grid">
                            <div class="jobai-info-item">
                                <label>Job Title:</label>
                                <input type="text" id="jobai-title" placeholder="Auto-detected or enter manually">
                            </div>
                            <div class="jobai-info-item">
                                <label>Company:</label>
                                <input type="text" id="jobai-company" placeholder="Auto-detected or enter manually">
                            </div>
                            <div class="jobai-info-item">
                                <label>Location:</label>
                                <input type="text" id="jobai-location" placeholder="Auto-detected or enter manually">
                            </div>
                            <div class="jobai-info-item">
                                <label>Description:</label>
                                <textarea id="jobai-description" placeholder="Auto-detected description" rows="2"></textarea>
                            </div>
                            <div class="jobai-info-item">
                                <label>Applied Date:</label>
                                <input type="date" id="jobai-applied-date" value="${new Date().toISOString().split('T')[0]}">
                            </div>
                            <div class="jobai-info-item">
                                <label>Status:</label>
                                <select id="jobai-status">
                                    <option value="Applied">Applied</option>
                                    <option value="In Progress">In Progress</option>
                                    <option value="Interview">Interview</option>
                                    <option value="Offer">Offer</option>
                                    <option value="Rejected">Rejected</option>
                                    <option value="Withdrawn">Withdrawn</option>
                                </select>
                            </div>
                            <div class="jobai-info-item">
                                <label>Notes (Skills & Requirements):</label>
                                <textarea id="jobai-notes" placeholder="Auto-detected skills and requirements" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="jobai-widget-actions">
                        <button class="jobai-btn jobai-btn-primary" id="jobai-extract-btn">
                            <span class="jobai-btn-text">Extract Job Info</span>
                            <div class="jobai-btn-spinner" style="display: none;"></div>
                        </button>
                        <button class="jobai-btn jobai-btn-success" id="jobai-send-btn" style="display: none;">
                            <span class="jobai-btn-text">Send to JOB-AI</span>
                            <div class="jobai-btn-spinner" style="display: none;"></div>
                        </button>
                    </div>
                    
                    <div class="jobai-widget-footer">
                        <small>Selected ${this.selectedText.length > 100 ? '100+' : this.selectedText.length} characters</small>
                    </div>
                </div>
            </div>
        `;
    }

    applyWidgetStyles() {
        const style = document.createElement('style');
        style.textContent = `            #jobai-extraction-widget {
                position: fixed;
                z-index: 10000;
                background: white;
                border: 1px solid #e5e7eb;
                border-radius: 16px;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                width: 360px;
                max-height: 500px;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }
            
            .jobai-widget-container {
                display: flex;
                flex-direction: column;
                height: 100%;
                max-height: 500px;
                padding: 20px;
            }
        
            
            .jobai-widget-header {
                background: linear-gradient(135deg, #3b82f6, #06b6d4);
                color: white;
                padding: 10px 24px !important;
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 12px;
            }
            
            .jobai-logo-text {
                font-weight: 700;
                font-size: 18px;
                letter-spacing: -0.02em;
            }
            
            .jobai-widget-close {
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                padding: 8px;
                border-radius: 6px;
                transition: background-color 0.2s;
                line-height: 1;
            }
            
            .jobai-widget-close:hover {
                background: rgba(255, 255, 255, 0.15);
            }
            
            .jobai-widget-content {
                padding: 10px !important;
                flex: 1;
                display: flex;
                flex-direction: column;
                overflow-y: auto;
                gap: 15px;
            }
            
            .jobai-extraction-status {
                display: flex;
                align-items: center;
                gap: 16px;
                padding: 16px !important;
                background: #f0f9ff;
                border-radius: 12px;
                border: 1px solid #e0f2fe;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
            }
            
            .jobai-status-icon {
                font-size: 28px;
                flex-shrink: 0;
            }
            
            .jobai-status-text strong {
                display: block;
                color: #0f172a;
                font-size: 16px;
                margin-bottom: 4px;
                font-weight: 600;
            }
            
            .jobai-status-text p {
                color: #64748b;
                font-size: 14px;
                margin: 0;
                line-height: 1.4;
            }
            
            .jobai-extracted-info {
                margin-bottom: 24px;
            }
            
            .jobai-extracted-info h4 {
                font-size: 16px;
                margin-bottom: 16px;
                color: #0f172a;
                font-weight: 600;
            }
            
            .jobai-info-grid {
                display: flex;
                flex-direction: column;
                gap: 16px;
            }
            
            .jobai-info-item {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            
            .jobai-info-item label {
                font-size: 14px;
                font-weight: 600;
                color: #374151;
            }
              .jobai-info-item input,
            .jobai-info-item textarea,
            .jobai-info-item select {
                padding: 12px 16px;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                font-size: 14px;
                font-family: inherit;
                transition: border-color 0.2s, box-shadow 0.2s;
                line-height: 1.4;
                background: white;
            }
            
            .jobai-info-item input:focus,
            .jobai-info-item textarea:focus,
            .jobai-info-item select:focus {
                outline: none;
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
            
            .jobai-widget-actions {
                display: flex;
                flex-direction: column;
                gap: 14px;
                margin-bottom: 20px;
                padding-top: 12px;
                padding-bottom: 12px;
            }
            
            .jobai-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
                padding: 14px 20px;
                border: none;
                border-radius: 10px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s;
                min-height: 48px;
            }
            
            .jobai-btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }
            
            .jobai-btn-primary {
                background: #3b82f6;
                color: white;
            }
            
            .jobai-btn-primary:hover:not(:disabled) {
                background: #2563eb;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
            }
            
            .jobai-btn-success {
                background: #10b981;
                color: white;
            }
            
            .jobai-btn-success:hover:not(:disabled) {
                background: #059669;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
            }
            
            .jobai-btn-spinner {
                width: 16px;
                height: 16px;
                border: 2px solid transparent;
                border-top: 2px solid currentColor;
                border-radius: 50%;
                animation: jobai-spin 1s linear infinite;
            }
            
            .jobai-widget-footer {
                text-align: center;
                color: #6b7280;
                border-top: 1px solid #f3f4f6;
                padding: 16px 24px;
                padding-top: 24px;
                font-size: 13px;
            }
            
            @keyframes jobai-spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;

        document.head.appendChild(style);
    }    setupWidgetListeners() {
        // Close button - only way to close the widget
        const closeBtn = this.widget.querySelector('#jobai-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                console.log('Close button clicked - hiding widget');
                this.hideWidget();
            });
        }

        // Extract button
        const extractBtn = this.widget.querySelector('#jobai-extract-btn');
        if (extractBtn) {
            extractBtn.addEventListener('click', () => {
                console.log('Extract button clicked');
                this.extractJobInfo();
            });
        }

        // Send button
        const sendBtn = this.widget.querySelector('#jobai-send-btn');
        if (sendBtn) {
            sendBtn.addEventListener('click', () => {
                console.log('Send button clicked');
                this.sendToJobAI();
            });
        }

        // No more general click handlers - widget stays open until close button is clicked
    }showWidget(e) {
        if (!this.widget) return;

        // Position widget near the selection with better spacing
        const rect = this.selectionRange.getBoundingClientRect();
        const widgetWidth = 360; // Updated to match CSS
        const widgetHeight = 450; // Slightly larger for additional fields

        let left = rect.right + 20; // More spacing from selection
        let top = rect.top;

        // Adjust if widget would go off-screen
        if (left + widgetWidth > window.innerWidth) {
            left = rect.left - widgetWidth - 20;
        }

        if (top + widgetHeight > window.innerHeight) {
            top = window.innerHeight - widgetHeight - 30; // More margin from bottom
        }

        // Ensure widget stays on screen with better margins
        left = Math.max(20, Math.min(left, window.innerWidth - widgetWidth - 20));
        top = Math.max(20, top);

        this.widget.style.left = `${left}px`;
        this.widget.style.top = `${top}px`;
        this.widget.style.display = 'block';

        // Update character count
        const footer = this.widget.querySelector('.jobai-widget-footer small');
        footer.textContent = `Selected ${this.selectedText.length > 100 ? '100+' : this.selectedText.length} characters`;

        this.isWidgetVisible = true;
    }

    hideWidget() {
        if (this.widget) {
            this.widget.style.display = 'none';
            this.isWidgetVisible = false;

            // Reset widget state
            this.resetWidget();
        }
    } resetWidget() {
        const extractedInfo = this.widget.querySelector('#jobai-extracted-info');
        const extractBtn = this.widget.querySelector('#jobai-extract-btn');
        const sendBtn = this.widget.querySelector('#jobai-send-btn');

        extractedInfo.style.display = 'none';
        extractBtn.style.display = 'flex';
        sendBtn.style.display = 'none';

        // Clear all form fields (no URL field)
        this.widget.querySelector('#jobai-title').value = '';
        this.widget.querySelector('#jobai-company').value = '';
        this.widget.querySelector('#jobai-location').value = '';
        this.widget.querySelector('#jobai-description').value = '';
        this.widget.querySelector('#jobai-applied-date').value = new Date().toISOString().split('T')[0];
        this.widget.querySelector('#jobai-status').value = 'Applied';
        this.widget.querySelector('#jobai-notes').value = '';
    } async extractJobInfo() {
        const extractBtn = this.widget.querySelector('#jobai-extract-btn');
        const extractedInfo = this.widget.querySelector('#jobai-extracted-info');

        this.setButtonLoading(extractBtn, true);

        try {
            // Check if user is authenticated
            const isAuthenticated = await this.checkAuthentication();
            if (!isAuthenticated) {
                this.showNotification('Please log in to the JOB-AI extension first', 'error');
                this.setButtonLoading(extractBtn, false);
                return;
            }

            // Use RAG-powered extraction ONLY for parsing (no database save)
            const jobData = {
                text: this.selectedText,
                description: this.selectedText,
                sourceUrl: window.location.href,
                pageUrl: window.location.href,
                extractedBy: 'widget-extraction-only',
                extractedAt: new Date().toISOString()
            };
            // Send to background script for RAG processing ONLY (no save)
            const response = await chrome.runtime.sendMessage({
                action: 'parse-only',
                data: jobData
            });

            console.log('Parse-only response:', response); // Debug log

            if (response.success && response.parsedResult) {
                const extractedData = response.parsedResult;

                // Populate all form fields with RAG-extracted data matching database schema
                this.widget.querySelector('#jobai-title').value = extractedData.title || '';
                this.widget.querySelector('#jobai-company').value = extractedData.company || '';
                this.widget.querySelector('#jobai-location').value = extractedData.location || '';
                this.widget.querySelector('#jobai-description').value = extractedData.description || this.selectedText.substring(0, 500);
                this.widget.querySelector('#jobai-notes').value = extractedData.notes || '';

                // Applied date and status are already set with defaults

                // Show extracted info and send button
                extractedInfo.style.display = 'block';
                extractBtn.style.display = 'none';
                this.widget.querySelector('#jobai-send-btn').style.display = 'flex';

                this.showNotification('Job information extracted! Click "Send to JOB-AI" to save.', 'success');
            } else {
                console.error('Parse failed. Response:', response);
                throw new Error(response.error || 'RAG extraction failed');
            }
        } catch (error) {
            console.error('Error extracting job info:', error);
            this.showNotification('Failed to extract job information: ' + error.message, 'error');
        } finally {
            this.setButtonLoading(extractBtn, false);
        }
    }

    async parseJobDescription(text) {
        // Simple parsing logic - can be enhanced with AI
        const data = {
            title: this.extractJobTitle(text),
            company: this.extractCompanyName(text),
            location: this.extractLocation(text),
            skills: this.extractSkills(text)
        };

        return data;
    }

    extractJobTitle(text) {
        // Look for common job title patterns
        const titlePatterns = [
            /job title[:\s]+([^\n\r]+)/i,
            /position[:\s]+([^\n\r]+)/i,
            /role[:\s]+([^\n\r]+)/i,
            /^([^.\n\r]+(?:engineer|developer|manager|analyst|specialist|coordinator|director))/i
        ];

        for (const pattern of titlePatterns) {
            const match = text.match(pattern);
            if (match) {
                return match[1].trim();
            }
        }

        return '';
    }

    extractCompanyName(text) {
        // Look for company name patterns
        const companyPatterns = [
            /company[:\s]+([^\n\r]+)/i,
            /at\s+([A-Z][^\s,\n\r]+(?:\s+[A-Z][^\s,\n\r]+)*)/,
            /join\s+([A-Z][^\s,\n\r]+(?:\s+[A-Z][^\s,\n\r]+)*)/
        ];

        for (const pattern of companyPatterns) {
            const match = text.match(pattern);
            if (match) {
                return match[1].trim();
            }
        }

        return '';
    }

    extractLocation(text) {
        // Look for location patterns
        const locationPatterns = [
            /location[:\s]+([^\n\r]+)/i,
            /based in[:\s]+([^\n\r]+)/i,
            /([A-Z][a-z]+,\s*[A-Z]{2})/,
            /(remote|hybrid|on-site)/i
        ];

        for (const pattern of locationPatterns) {
            const match = text.match(pattern);
            if (match) {
                return match[1].trim();
            }
        }

        return '';
    }

    extractSkills(text) {
        const commonSkills = [
            'JavaScript', 'Python', 'Java', 'React', 'Node.js', 'SQL', 'HTML', 'CSS',
            'TypeScript', 'Angular', 'Vue.js', 'PHP', 'C++', 'C#', 'Go', 'Rust',
            'Docker', 'Kubernetes', 'AWS', 'Azure', 'GCP', 'MongoDB', 'PostgreSQL',
            'Git', 'Linux', 'Agile', 'Scrum', 'REST API', 'GraphQL', 'Machine Learning',
            'Data Science', 'AI', 'DevOps', 'CI/CD', 'Jenkins', 'Terraform'
        ];

        const foundSkills = [];
        const lowerText = text.toLowerCase();

        for (const skill of commonSkills) {
            if (lowerText.includes(skill.toLowerCase())) {
                foundSkills.push(skill);
            }
        }

        return foundSkills.join(', ');
    } async sendToJobAI() {
        const sendBtn = this.widget.querySelector('#jobai-send-btn');
        this.setButtonLoading(sendBtn, true);

        try {
            // Get current form data from all fields (matching database schema, excluding URL)
            const jobData = {
                title: this.widget.querySelector('#jobai-title').value,
                company: this.widget.querySelector('#jobai-company').value,
                location: this.widget.querySelector('#jobai-location').value,
                url: window.location.href, // Auto-set to current page URL
                description: this.widget.querySelector('#jobai-description').value,
                appliedDate: this.widget.querySelector('#jobai-applied-date').value,
                status: this.widget.querySelector('#jobai-status').value,
                notes: this.widget.querySelector('#jobai-notes').value,
                sourceUrl: window.location.href,
                extractedAt: new Date().toISOString(),
                extractedBy: 'widget-manual-save'
            };

            // Validate required fields
            if (!jobData.title || !jobData.company) {
                this.showNotification('Please fill in at least Job Title and Company', 'error');
                this.setButtonLoading(sendBtn, false);
                return;
            }

            // Send to backend via background script
            const response = await chrome.runtime.sendMessage({
                action: 'save-to-backend',
                data: jobData
            });

            if (response.success) {
                this.showNotification('✅ Job application saved successfully!', 'success');
                this.hideWidget();
            } else {
                this.showNotification('Failed to save job application', 'error');
            }

        } catch (error) {
            console.error('Error sending to JOB-AI:', error);
            this.showNotification('Failed to send job to JOB-AI', 'error');
        } finally {
            this.setButtonLoading(sendBtn, false);
        }
    } async sendToBackend(jobData) {
        try {
            const authToken = await this.getAuthToken();
            if (!authToken) {
                throw new Error('No authentication token');
            }

            // Replace with your actual JOB-AI backend URL
            const backendUrl = 'http://localhost:5000'; // Adjust as needed

            // Transform data to match your backend's expected format
            const transformedData = {
                title: jobData.title || 'Unknown Title',
                description: jobData.description || '',
                company: jobData.company || 'Unknown Company',
                location: jobData.location || '',
                skills: jobData.skills || '',
                sourceUrl: jobData.sourceUrl || '',
                appliedDate: new Date().toISOString().split('T')[0], // Format: YYYY-MM-DD
                status: 'extracted', // Default status
                extractedAt: jobData.extractedAt || new Date().toISOString(),
                extractedBy: 'chrome-extension'
            };

            const response = await fetch(`${backendUrl}/jobs/new-application`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify(transformedData)
            });

            return response.ok;

        } catch (error) {
            console.error('Backend API error:', error);
            return false;
        }
    }

    async checkAuthentication() {
        try {
            const result = await chrome.storage.local.get(['jobai_auth_token']);
            return !!result.jobai_auth_token;
        } catch (error) {
            console.error('Error checking authentication:', error);
            return false;
        }
    }

    async getAuthToken() {
        try {
            const result = await chrome.storage.local.get(['jobai_auth_token']);
            return result.jobai_auth_token;
        } catch (error) {
            console.error('Error getting auth token:', error);
            return null;
        }
    }

    setButtonLoading(button, isLoading) {
        const text = button.querySelector('.jobai-btn-text');
        const spinner = button.querySelector('.jobai-btn-spinner');

        if (isLoading) {
            button.disabled = true;
            text.style.display = 'none';
            spinner.style.display = 'block';
        } else {
            button.disabled = false;
            text.style.display = 'block';
            spinner.style.display = 'none';
        }
    }

    showNotification(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10001;
            padding: 12px 16px;
            border-radius: 8px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            font-size: 14px;
            font-weight: 500;
            color: white;
            background: ${type === 'success' ? '#10b981' : '#ef4444'};
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        notification.textContent = message;
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }    isTextSelectionClick(e) {
        // Check if this click is part of text selection
        const selection = window.getSelection();
        return selection && selection.toString().trim().length > 0 && e.detail > 1;
    }

    isSelectionEvent(e) {
        // Check if the click is related to text selection
        return e.detail > 1; // Double-click or triple-click
    }
}

// Initialize content script
const jobAIContentScript = new JobAIContentScript();
