/* JOB-AI Extension Popup Styles */
/* Inspired by the JOB-AI frontend design system */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    background: hsl(0 0% 100%);
    color: hsl(222.2 84% 4.9%);
    width: 380px;
    min-height: 500px;
}

.popup-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Header */
.header {
    background: linear-gradient(135deg, hsl(217 91.2% 59.8%), hsl(199 89% 48%));
    padding: 16px 20px;
    color: white;
    border-radius: 0 0 12px 12px;
    margin-bottom: 20px;
}

.logo-text {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: -0.02em;
}

/* Card Components */
.card {
    background: hsl(0 0% 100%);
    border: 1px solid hsl(214.3 31.8% 91.4%);
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    margin: 0 20px 16px;
}

.card-header {
    padding: 20px 20px 0;
    text-align: center;
}

.card-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
    color: hsl(222.2 84% 4.9%);
}

.card-description {
    font-size: 14px;
    color: hsl(215.4 16.3% 46.9%);
    margin-bottom: 16px;
}

.card-content {
    padding: 0 20px 20px;
}

.card-footer {
    padding: 0 20px 20px;
    text-align: center;
}

/* Form Elements */
.form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.label {
    font-size: 14px;
    font-weight: 500;
    color: hsl(222.2 84% 4.9%);
}

.input {
    height: 40px;
    padding: 8px 12px;
    border: 1px solid hsl(214.3 31.8% 91.4%);
    border-radius: 6px;
    font-size: 14px;
    background: hsl(0 0% 100%);
    color: hsl(222.2 84% 4.9%);
    transition: border-color 0.2s, box-shadow 0.2s;
}

.input:focus {
    outline: none;
    border-color: hsl(217 91.2% 59.8%);
    box-shadow: 0 0 0 2px hsl(217 91.2% 59.8% / 0.2);
}

.input::placeholder {
    color: hsl(215.4 16.3% 46.9%);
}

/* Button Components */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    cursor: pointer;
    border: none;
    height: 40px;
    padding: 0 16px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: hsl(217 91.2% 59.8%);
    color: hsl(210 40% 98%);
}

.btn-primary:hover:not(:disabled) {
    background: hsl(217 91.2% 55%);
}

.btn-ghost {
    background: transparent;
    color: hsl(222.2 47.4% 11.2%);
}

.btn-ghost:hover {
    background: hsl(210 40% 96.1%);
}

.btn-sm {
    height: 32px;
    padding: 0 12px;
    font-size: 13px;
}

/* Links */
.link {
    color: hsl(217 91.2% 59.8%);
    text-decoration: none;
    font-weight: 500;
}

.link:hover {
    text-decoration: underline;
}

.footer-text {
    font-size: 14px;
    color: hsl(215.4 16.3% 46.9%);
}

/* Main Section */
.main-section {
    padding: 0 20px;
}

.user-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: hsl(210 40% 96.1%);
    border-radius: 8px;
    margin-bottom: 20px;
}

.user-welcome {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.welcome-text {
    font-size: 14px;
    font-weight: 500;
    color: hsl(222.2 84% 4.9%);
}

.user-email {
    font-size: 12px;
    color: hsl(215.4 16.3% 46.9%);
}

/* Feature Section */
.feature-section {
    margin-bottom: 24px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: hsl(222.2 84% 4.9%);
}

.action-grid {
    display: grid;
    gap: 12px;
}

.action-card {
    padding: 16px;
    border: 1px solid hsl(214.3 31.8% 91.4%);
    border-radius: 8px;
    background: hsl(0 0% 100%);
    transition: border-color 0.2s, box-shadow 0.2s;
}

.action-card:hover {
    border-color: hsl(217 91.2% 59.8%);
    box-shadow: 0 2px 8px hsl(217 91.2% 59.8% / 0.1);
}

.action-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.action-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
    color: hsl(222.2 84% 4.9%);
}

.action-description {
    font-size: 12px;
    color: hsl(215.4 16.3% 46.9%);
    margin-bottom: 8px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-text {
    font-size: 11px;
    color: hsl(142.1 76.2% 36.3%);
    font-weight: 500;
}

/* Instructions */
.instructions {
    padding: 16px;
    background: hsl(210 40% 96.1%);
    border-radius: 8px;
    margin-bottom: 20px;
}

.instructions-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: hsl(222.2 84% 4.9%);
}

.instructions-list {
    font-size: 12px;
    color: hsl(215.4 16.3% 46.9%);
    padding-left: 16px;
}

.instructions-list li {
    margin-bottom: 4px;
}

/* Messages */
.message-container {
    position: fixed;
    top: 10px;
    left: 10px;
    right: 10px;
    z-index: 1000;
}

.message {
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
}

.message.success {
    background: hsl(142.1 76.2% 36.3% / 0.1);
    color: hsl(142.1 76.2% 36.3%);
    border: 1px solid hsl(142.1 76.2% 36.3% / 0.2);
}

.message.error {
    background: hsl(0 84.2% 60.2% / 0.1);
    color: hsl(0 84.2% 60.2%);
    border: 1px solid hsl(0 84.2% 60.2% / 0.2);
}

/* Spinner */
.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Auth Section */
.auth-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 400px) {
    body {
        width: 320px;
    }
    
    .card {
        margin: 0 16px 16px;
    }
    
    .main-section {
        padding: 0 16px;
    }
}
