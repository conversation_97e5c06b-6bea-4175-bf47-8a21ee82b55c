<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JOB-AI Extension</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="popup-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <span class="logo-text">JOB-AI</span>
            </div>
        </div>

        <!-- Authentication Section -->
        <div id="auth-section" class="auth-section">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Welcome to JOB-AI</h2>
                    <p class="card-description">Sign in to start extracting job offers</p>
                </div>
                <div class="card-content">
                    <form id="login-form" class="form">
                        <div class="form-group">
                            <label for="email" class="label">Email</label>
                            <input 
                                type="email" 
                                id="email" 
                                class="input" 
                                placeholder="<EMAIL>" 
                                required
                            >
                        </div>
                        <div class="form-group">
                            <label for="password" class="label">Password</label>
                            <input 
                                type="password" 
                                id="password" 
                                class="input" 
                                placeholder="Enter your password" 
                                required
                            >
                        </div>
                        <button type="submit" id="login-btn" class="btn btn-primary">
                            <span id="login-text">Sign In</span>
                            <div id="login-spinner" class="spinner" style="display: none;"></div>
                        </button>
                    </form>
                </div>
                <div class="card-footer">
                    <p class="footer-text">Don't have an account? 
                        <a href="#" id="register-link" class="link">Sign up</a>
                    </p>
                </div>
            </div>

            <!-- Register Form (Hidden by default) -->
            <div id="register-form" class="card" style="display: none;">
                <div class="card-header">
                    <h2 class="card-title">Create Account</h2>
                    <p class="card-description">Join JOB-AI to enhance your job search</p>
                </div>
                <div class="card-content">
                    <form id="signup-form" class="form">
                        <div class="form-group">
                            <label for="reg-email" class="label">Email</label>
                            <input 
                                type="email" 
                                id="reg-email" 
                                class="input" 
                                placeholder="<EMAIL>" 
                                required
                            >
                        </div>
                        <div class="form-group">
                            <label for="reg-password" class="label">Password</label>
                            <input 
                                type="password" 
                                id="reg-password" 
                                class="input" 
                                placeholder="Choose a password" 
                                required
                            >
                        </div>
                        <button type="submit" id="register-btn" class="btn btn-primary">
                            <span id="register-text">Create Account</span>
                            <div id="register-spinner" class="spinner" style="display: none;"></div>
                        </button>
                    </form>
                </div>
                <div class="card-footer">
                    <p class="footer-text">Already have an account? 
                        <a href="#" id="login-link" class="link">Sign in</a>
                    </p>
                </div>
            </div>
        </div>

        <!-- Main Dashboard (Hidden by default) -->
        <div id="main-section" class="main-section" style="display: none;">
            <div class="user-info">
                <div class="user-welcome">
                    <span class="welcome-text">Welcome back!</span>
                    <span id="user-email" class="user-email"></span>
                </div>
                <button id="logout-btn" class="btn btn-ghost btn-sm">Logout</button>
            </div>

            <div class="feature-section">
                <h3 class="section-title">Quick Actions</h3>
                <div class="action-grid">
                    <div class="action-card">
                        <div class="action-icon">🎯</div>
                        <h4 class="action-title">Extract Job</h4>
                        <p class="action-description">Select job text to extract and analyze</p>
                        <div class="status-indicator">
                            <span class="status-text">Ready to extract</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="instructions">
                <h4 class="instructions-title">How to use:</h4>
                <ol class="instructions-list">
                    <li>Navigate to any job listing page</li>
                    <li>Select the job description text</li>
                    <li>Click on the JOB-AI widget that appears</li>
                    <li>Review and send to JOB-AI for analysis</li>
                </ol>
            </div>
        </div>

        <!-- Error/Success Messages -->
        <div id="message-container" class="message-container" style="display: none;">
            <div id="message" class="message"></div>
        </div>
    </div>    <!-- Firebase SDKs - Local files for extension compatibility -->
    <script src="../firebase-sdk/firebase-app.js"></script>
    <script src="../firebase-sdk/firebase-auth.js"></script>
    
    <!-- Extension Scripts -->
    <script src="popup.js"></script>
</body>
</html>
