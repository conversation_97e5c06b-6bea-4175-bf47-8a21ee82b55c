// JOB-AI Extension Popup JavaScript
// Simplified and functional popup without Firebase

console.log('JOB-AI popup script loaded');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing popup...');
    initializePopup();
});

function initializePopup() {
    try {
        console.log('Setting up event listeners...');
        setupEventListeners();
        
        console.log('Checking auth status...');
        checkAuthStatus();
        
        console.log('Popup initialized successfully');
    } catch (error) {
        console.error('Popup initialization error:', error);
        showMessage('Failed to initialize extension: ' + error.message, 'error');
    }
}

function setupEventListeners() {
    // Login form
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
        console.log('Login form listener added');
    }

    // Register form
    const signupForm = document.getElementById('signup-form');
    if (signupForm) {
        signupForm.addEventListener('submit', handleRegister);
        console.log('Register form listener added');
    }

    // Switch to register
    const registerLink = document.getElementById('register-link');
    if (registerLink) {
        registerLink.addEventListener('click', function(e) {
            e.preventDefault();
            showRegisterForm();
        });
    }

    // Switch to login
    const loginLink = document.getElementById('login-link');
    if (loginLink) {
        loginLink.addEventListener('click', function(e) {
            e.preventDefault();
            showLoginForm();
        });
    }

    // Logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
}

async function checkAuthStatus() {
    try {
        console.log('Checking stored auth data...');
        
        // First test backend connectivity
        await testBackendConnection();
        
        const result = await chrome.storage.local.get(['jobai_auth_token', 'jobai_user_email']);
        console.log('Auth check result:', result);
        
        if (result.jobai_auth_token && result.jobai_user_email) {
            console.log('User is authenticated, showing main section');
            showMainSection(result.jobai_user_email);
        } else {
            console.log('User not authenticated, showing auth section');
            showAuthSection();
        }
    } catch (error) {
        console.error('Auth check error:', error);
        showAuthSection();
    }
}

async function testBackendConnection() {
    try {
        console.log('Testing backend connection...');
        
        // Test main health endpoint
        const healthResponse = await fetch('http://localhost:5000/health', {
            method: 'GET',
            timeout: 5000 // 5 second timeout
        });
        
        if (healthResponse.ok) {
            console.log('Backend health check successful');
            
            // Test auth routes
            const authResponse = await fetch('http://localhost:5000/auth/status', {
                method: 'GET',
                timeout: 5000
            });
            
            if (authResponse.ok) {
                const authData = await authResponse.json();
                console.log('Auth routes working:', authData);
                return true;
            } else {
                console.warn('Auth routes not responding:', authResponse.status);
                showMessage('Auth routes not available. Please restart backend.', 'warning');
                return false;
            }
        } else {
            console.warn('Backend returned non-200 status:', healthResponse.status);
            showMessage('Backend connection issue. Please check if backend is running.', 'warning');
            return false;
        }
    } catch (error) {
        console.error('Backend connection failed:', error);
        showMessage('Cannot connect to backend. Please ensure backend is running on localhost:5000', 'error');
        return false;
    }
}

async function handleLogin(e) {
    e.preventDefault();
    console.log('Login attempt started');
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    if (!email || !password) {
        showMessage('Please fill in all fields', 'error');
        return;
    }

    setLoadingState('login', true);

    try {
        console.log('Attempting login with backend...');
        const response = await fetch('http://localhost:5000/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password })
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        // Get response text first to see what we're getting
        const responseText = await response.text();
        console.log('Raw response:', responseText);

        // Try to parse as JSON
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            console.error('Response was not JSON:', responseText);
            throw new Error('Backend returned invalid response. Please check if backend is running.');
        }

        console.log('Parsed login response:', response.status, data);

        if (response.ok && data.access_token) {
            // Store auth data
            await chrome.storage.local.set({
                jobai_auth_token: data.access_token,
                jobai_user_email: email,
                jobai_auth_timestamp: Date.now()
            });

            showMessage('Login successful!', 'success');
            setTimeout(() => showMainSection(email), 1000);
        } else {
            throw new Error(data.error || data.message || 'Login failed');
        }
    } catch (error) {
        console.error('Login error:', error);
        if (error.message.includes('fetch')) {
            showMessage('Cannot connect to backend. Please ensure backend is running on localhost:5000', 'error');
        } else {
            showMessage(error.message || 'Login failed. Please try again.', 'error');
        }
    } finally {
        setLoadingState('login', false);
    }
}

async function handleRegister(e) {
    e.preventDefault();
    console.log('Register attempt started');
    
    const email = document.getElementById('reg-email').value;
    const password = document.getElementById('reg-password').value;
    
    if (!email || !password) {
        showMessage('Please fill in all fields', 'error');
        return;
    }

    if (password.length < 6) {
        showMessage('Password must be at least 6 characters', 'error');
        return;
    }

    setLoadingState('register', true);

    try {
        console.log('Attempting registration with backend...');
        const response = await fetch('http://localhost:5000/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password })
        });

        console.log('Response status:', response.status);
        
        // Get response text first to see what we're getting
        const responseText = await response.text();
        console.log('Raw response:', responseText);

        // Try to parse as JSON
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            console.error('Response was not JSON:', responseText);
            throw new Error('Backend returned invalid response. Please check if backend is running.');
        }

        console.log('Parsed register response:', response.status, data);

        if (response.ok && data.access_token) {
            // Store auth data
            await chrome.storage.local.set({
                jobai_auth_token: data.access_token,
                jobai_user_email: email,
                jobai_auth_timestamp: Date.now()
            });

            showMessage('Account created successfully!', 'success');
            setTimeout(() => showMainSection(email), 1000);
        } else {
            throw new Error(data.error || data.message || 'Registration failed');
        }
    } catch (error) {
        console.error('Register error:', error);
        if (error.message.includes('fetch')) {
            showMessage('Cannot connect to backend. Please ensure backend is running on localhost:5000', 'error');
        } else {
            showMessage(error.message || 'Registration failed. Please try again.', 'error');
        }
    } finally {
        setLoadingState('register', false);
    }
}

async function handleLogout() {
    try {
        console.log('Logging out...');
        await chrome.storage.local.remove(['jobai_auth_token', 'jobai_user_email', 'jobai_auth_timestamp']);
        showMessage('Logged out successfully', 'success');
        setTimeout(() => showAuthSection(), 1000);
    } catch (error) {
        console.error('Logout error:', error);
        showMessage('Error logging out', 'error');
    }
}

function showAuthSection() {
    console.log('Showing auth section');
    const authSection = document.getElementById('auth-section');
    const mainSection = document.getElementById('main-section');
    
    if (authSection) authSection.style.display = 'block';
    if (mainSection) mainSection.style.display = 'none';
}

function showMainSection(email) {
    console.log('Showing main section for:', email);
    const authSection = document.getElementById('auth-section');
    const mainSection = document.getElementById('main-section');
    const userEmailEl = document.getElementById('user-email');
    
    if (authSection) authSection.style.display = 'none';
    if (mainSection) mainSection.style.display = 'block';
    if (userEmailEl) userEmailEl.textContent = email;
}

function showLoginForm() {
    console.log('Showing login form');
    const authSection = document.getElementById('auth-section');
    if (authSection) {
        const loginCard = authSection.querySelector('.card:first-child');
        const registerCard = document.getElementById('register-form');
        
        if (loginCard) loginCard.style.display = 'block';
        if (registerCard) registerCard.style.display = 'none';
    }
}

function showRegisterForm() {
    console.log('Showing register form');
    const authSection = document.getElementById('auth-section');
    if (authSection) {
        const loginCard = authSection.querySelector('.card:first-child');
        const registerCard = document.getElementById('register-form');
        
        if (loginCard) loginCard.style.display = 'none';
        if (registerCard) registerCard.style.display = 'block';
    }
}

function setLoadingState(type, loading) {
    const button = document.getElementById(`${type}-btn`);
    const text = document.getElementById(`${type}-text`);
    const spinner = document.getElementById(`${type}-spinner`);
    
    if (button) button.disabled = loading;
    if (text) text.style.display = loading ? 'none' : 'block';
    if (spinner) spinner.style.display = loading ? 'block' : 'none';
}

function showMessage(message, type = 'info') {
    console.log('Showing message:', type, message);
    const container = document.getElementById('message-container');
    const messageEl = document.getElementById('message');
    
    if (container && messageEl) {
        messageEl.textContent = message;
        messageEl.className = `message ${type}`;
        container.style.display = 'block';
        
        setTimeout(() => {
            container.style.display = 'none';
        }, 3000);
    }
}
