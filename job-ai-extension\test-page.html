<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JOB-AI Extension Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .job-posting {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #f9fafb;
        }
        h1, h2 {
            color: #1f2937;
        }
        .highlight {
            background: #fef3c7;
            padding: 2px 4px;
            border-radius: 4px;
        }
        .instructions {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border-left: 4px solid #10b981;
            background: #f0fdf4;
        }
    </style>
</head>
<body>
    <h1>🧪 JOB-AI Extension Test Page</h1>

    <div class="instructions">
        <h2>🔧 Testing Instructions for Notes Feature</h2>
        <p><strong>What's New:</strong> The RAG parser now extracts required skills, experience, and qualifications into the <strong>Notes</strong> field!</p>
        
        <h3>How to Test:</h3>
        <ol>
            <li>✅ Load the JOB-AI extension in Chrome</li>
            <li>✅ Log in to the extension using your credentials</li>
            <li>✅ Select any job description text below (especially sections with requirements)</li>
            <li>✅ Use the extension widget or right-click context menu to extract job info</li>
            <li>✅ Check the JOB-AI dashboard to verify the <strong>Notes</strong> field contains extracted skills and requirements</li>
        </ol>
        
        <p><strong>Expected Result:</strong> Notes field should contain: "Compétences requises: [technologies]; Expérience: [années]; Autres exigences: [diplômes, langues, etc.]"</p>
    </div>

    <div class="test-section">
        <h2>🎯 Test Job Posting #1 - Software Engineer</h2>
        <div class="job-posting">
            <h3>Senior Software Engineer - Full Stack</h3>
            <p><strong>Company:</strong> TechCorp Industries</p>
            <p><strong>Location:</strong> San Francisco, CA (Hybrid)</p>
            <p><strong>Salary:</strong> $120,000 - $180,000</p>
            
            <h4>Job Description:</h4>
            <p>We are seeking a talented Senior Software Engineer to join our dynamic team. You will be responsible for developing scalable web applications using modern technologies including <span class="highlight">React, Node.js, TypeScript, and PostgreSQL</span>.</p>
            
            <h4>Requirements:</h4>
            <ul>
                <li>5+ years of experience in software development</li>
                <li>Proficiency in <span class="highlight">JavaScript, Python, and SQL</span></li>
                <li>Experience with <span class="highlight">AWS, Docker, and Kubernetes</span></li>
                <li>Strong knowledge of <span class="highlight">REST APIs and GraphQL</span></li>
                <li>Familiarity with <span class="highlight">Agile development methodologies</span></li>
            </ul>
            
            <h4>Nice to Have:</h4>
            <ul>
                <li>Experience with <span class="highlight">Machine Learning and AI</span></li>
                <li>Knowledge of <span class="highlight">DevOps practices and CI/CD</span></li>
                <li>Contributions to open-source projects</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 Test Job Posting #2 - Data Scientist</h2>
        <div class="job-posting">
            <h3>Data Scientist - AI/ML Team</h3>
            <p><strong>Company:</strong> DataFlow Analytics</p>
            <p><strong>Location:</strong> Remote (US Only)</p>
            <p><strong>Salary:</strong> $140,000 - $200,000</p>
            
            <h4>Position Overview:</h4>
            <p>Join our cutting-edge AI team to develop machine learning models that power our analytics platform. You'll work with large datasets and implement state-of-the-art algorithms using <span class="highlight">Python, TensorFlow, and PyTorch</span>.</p>
            
            <h4>Key Responsibilities:</h4>
            <ul>
                <li>Design and implement ML pipelines using <span class="highlight">Python and Scikit-learn</span></li>
                <li>Work with big data tools like <span class="highlight">Spark and Hadoop</span></li>
                <li>Develop predictive models using <span class="highlight">Deep Learning and NLP</span></li>
                <li>Collaborate with engineering teams on model deployment</li>
            </ul>
            
            <h4>Required Skills:</h4>
            <ul>
                <li>PhD or Master's in Computer Science, Statistics, or related field</li>
                <li>3+ years of experience in <span class="highlight">Data Science and Machine Learning</span></li>
                <li>Expertise in <span class="highlight">Python, R, and SQL</span></li>
                <li>Experience with <span class="highlight">cloud platforms (AWS, GCP, Azure)</span></li>
                <li>Strong background in <span class="highlight">statistics and mathematics</span></li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 Test Job Posting #3 - Frontend Developer</h2>
        <div class="job-posting">
            <h3>Frontend Developer - React Specialist</h3>
            <p><strong>Company:</strong> WebDev Solutions</p>
            <p><strong>Location:</strong> Austin, TX (On-site)</p>
            <p><strong>Salary:</strong> $85,000 - $120,000</p>
            
            <h4>About the Role:</h4>
            <p>We're looking for a passionate Frontend Developer to create beautiful, responsive user interfaces. You'll work primarily with <span class="highlight">React, TypeScript, and modern CSS frameworks</span> to build engaging web applications.</p>
            
            <h4>Technical Requirements:</h4>
            <ul>
                <li>3+ years of experience with <span class="highlight">React and JavaScript</span></li>
                <li>Proficiency in <span class="highlight">HTML5, CSS3, and SCSS</span></li>
                <li>Experience with <span class="highlight">Redux or other state management</span></li>
                <li>Knowledge of <span class="highlight">Webpack, Babel, and build tools</span></li>
                <li>Familiarity with <span class="highlight">responsive design and mobile-first development</span></li>
            </ul>
            
            <h4>Bonus Points:</h4>
            <ul>
                <li>Experience with <span class="highlight">Next.js or Gatsby</span></li>
                <li>Knowledge of <span class="highlight">testing frameworks (Jest, Cypress)</span></li>
                <li>Understanding of <span class="highlight">accessibility standards (WCAG)</span></li>
            </ul>
        </div>
    </div>

    <script>
        // Add some JavaScript to simulate a real job site
        document.addEventListener('DOMContentLoaded', function() {
            console.log('JOB-AI Extension Test Page loaded');
            console.log('You can now test the extension by selecting job description text');
            
            // Add click handlers to highlight important sections
            const highlights = document.querySelectorAll('.highlight');
            highlights.forEach(el => {
                el.addEventListener('click', function() {
                    this.style.background = '#fbbf24';
                    setTimeout(() => {
                        this.style.background = '#fef3c7';
                    }, 200);
                });
            });
        });
    </script>
</body>
</html>
